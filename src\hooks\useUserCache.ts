/**
 * User Cache Hook - 2025 Simplified
 *
 * Automatically populates user cache from ticket and message data
 * Ensures consistent user profiles across all components
 */

import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useSupabaseClient } from '@/lib/supabase-clerk';
import { getUserCacheService } from '@/lib/services/user-cache.service';
import type {
  Ticket,
  TicketMessage,
} from '@/features/ticketing/models/ticket.schema';

/**
 * Populate user cache from ticket data
 * Call this whenever ticket data is fetched or updated
 */
export function usePopulateUserCacheFromTickets(
  tickets: Ticket[] | undefined,
  tenantId: string
) {
  const queryClient = useQueryClient();
  const { supabase } = useSupabaseClient();

  useEffect(() => {
    if (!tickets || tickets.length === 0) return;

    const userCache = getUserCacheService(queryClient, supabase);

    // Extract and cache all user data from tickets
    tickets.forEach((ticket) => {
      userCache.extractAndCacheFromTicket(ticket, tenantId);
    });
  }, [tickets, tenantId, queryClient, supabase]);
}

/**
 * Populate user cache from message data
 */
export function usePopulateUserCacheFromMessages(
  messages: TicketMessage[] | undefined,
  tenantId: string
) {
  const queryClient = useQueryClient();
  const { supabase } = useSupabaseClient();

  useEffect(() => {
    if (!messages || messages.length === 0) return;

    const userCache = getUserCacheService(queryClient, supabase);

    // Extract and cache all user data from messages
    messages.forEach((message) => {
      userCache.extractAndCacheFromMessage(message, tenantId);
    });
  }, [messages, tenantId, queryClient, supabase]);
}

/**
 * Get cached user profile
 * Returns cached data immediately, fetches if not available
 */
export function useCachedUserProfile(
  userId: string | undefined,
  tenantId: string
) {
  const queryClient = useQueryClient();

  if (!userId) return null;

  // This will return cached data if available
  // The actual fetching happens inside the service if needed
  return queryClient.getQueryData(['users', tenantId, 'detail', userId]);
}
