/**
 * Centralized Query Keys Factory - 2025 Optimized
 *
 * All query keys must include tenant_id as first element after the resource name
 * for proper tenant isolation and cache separation.
 */

export interface TicketFilters {
  status?: string[];
  priority?: string[];
  assignedTo?: string;
  createdBy?: string;
  roleFilter?: 'new' | 'assigned' | 'all';
}

export interface UserFilters {
  role?: string[];
  status?: string[];
  search?: string;
}

export const QueryKeys = {
  // Tickets
  TICKETS: {
    all: (tenantId: string) => ['tickets', tenantId] as const,
    list: (tenantId: string, filters?: TicketFilters) =>
      [...QueryKeys.TICKETS.all(tenantId), 'list', filters] as const,
    detail: (tenantId: string, ticketId: string) =>
      [...QueryKeys.TICKETS.all(tenantId), 'detail', ticketId] as const,
    messages: (tenantId: string, ticketId: string) =>
      [...QueryKeys.TICKETS.detail(tenantId, ticketId), 'messages'] as const,
    attachments: (tenantId: string, ticketId: string) =>
      [...QueryKeys.TICKETS.detail(tenantId, ticketId), 'attachments'] as const,
  },

  // Users
  USERS: {
    all: (tenantId: string) => ['users', tenantId] as const,
    list: (tenantId: string, filters?: UserFilters) =>
      [...QueryKeys.USERS.all(tenantId), 'list', filters] as const,
    detail: (tenantId: string, userId: string) =>
      [...QueryKeys.USERS.all(tenantId), 'detail', userId] as const,
    search: (tenantId: string, query: string) =>
      [...QueryKeys.USERS.all(tenantId), 'search', query] as const,
  },

  // Tenant-specific data
  TENANT: {
    all: (tenantId: string) => ['tenant', tenantId] as const,
    settings: (tenantId: string) =>
      [...QueryKeys.TENANT.all(tenantId), 'settings'] as const,
    stats: (tenantId: string) =>
      [...QueryKeys.TENANT.all(tenantId), 'stats'] as const,
  },

  // Real-time data (shorter cache times)
  REALTIME: {
    all: (tenantId: string) => ['realtime', tenantId] as const,
    notifications: (tenantId: string, userId: string) =>
      [...QueryKeys.REALTIME.all(tenantId), 'notifications', userId] as const,
    presence: (tenantId: string) =>
      [...QueryKeys.REALTIME.all(tenantId), 'presence'] as const,
  },
} as const;

/**
 * Helper function to invalidate all queries for a specific tenant
 * Useful for logout or tenant switching scenarios
 */
export const getTenantQueryPattern = (tenantId: string) => ({
  predicate: (query: { queryKey: unknown }) => {
    const queryKey = query.queryKey;
    return Array.isArray(queryKey) && queryKey.includes(tenantId);
  },
});

/**
 * Cache timing configurations - 2025 Enhanced Validation Strategy
 * Cache-first approach with silent background validation for all data types
 * CRITICAL FIX: Implement stale-while-revalidate pattern with guaranteed freshness
 */
export const CACHE_CONFIG = {
  // Main ticket data - instant cache-first loading with silent background validation
  tickets: {
    staleTime: 0, // Always validate in background while serving cached data first
    gcTime: 2 * 60 * 60 * 1000, // 2 hours - keep cache alive for performance
    refetchOnWindowFocus: true, // Validate when tab gains focus
    refetchOnMount: 'always' as const, // CRITICAL: Always validate on browser refresh
    refetchOnReconnect: true, // Validate when network reconnects
    // Serve cached data immediately while validating in background
    placeholderData: (previousData: any) => previousData,
  },

  // Messages - real-time feel with instant cache serving + background validation
  messages: {
    staleTime: 0, // Always validate in background for real-time messaging
    gcTime: 60 * 60 * 1000, // 1 hour
    refetchOnWindowFocus: true, // Validate when tab gains focus
    refetchOnMount: 'always' as const, // Always validate on page refresh
    refetchOnReconnect: true, // Validate when network reconnects
    placeholderData: (previousData: any) => previousData,
  },

  // User data - stable cache with periodic validation
  users: {
    staleTime: 0, // Validate in background to catch role/permission changes
    gcTime: 4 * 60 * 60 * 1000, // 4 hours
    refetchOnWindowFocus: false, // Users change less frequently, avoid excessive requests
    refetchOnMount: 'always' as const, // Still validate on page refresh for security
    refetchOnReconnect: true, // Validate when network reconnects
    placeholderData: (previousData: any) => previousData,
  },

  // Settings - stable data with background validation
  settings: {
    staleTime: 0, // Validate in background to catch configuration changes
    gcTime: 8 * 60 * 60 * 1000, // 8 hours
    refetchOnWindowFocus: false, // Settings change infrequently
    refetchOnMount: 'always' as const, // Always validate on page refresh
    refetchOnReconnect: true, // Validate when network reconnects
    placeholderData: (previousData: any) => previousData,
  },
} as const;

/**
 * Simple retry and timeout configurations
 */
export const RETRY_CONFIG = {
  defaultRetries: 3,
  retryDelay: 1000, // 1 second
  mutationTimeout: 10000, // 10 seconds
} as const;
