'use client';

import { QueryClient } from '@tanstack/react-query';
import { PersistQueryClientProvider } from '@tanstack/react-query-persist-client';
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';
import { useState } from 'react';
import { TanStackMessageCache } from '@/lib/cache/tanstack-db-cache';

// IndexedDB persister for optimal 2025 pattern
const createPersister = () => {
  return createSyncStoragePersister({
    storage: {
      getItem: (key: string) => {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : null;
      },
      setItem: (key: string, value: unknown) => {
        localStorage.setItem(key, JSON.stringify(value));
      },
      removeItem: (key: string) => {
        localStorage.removeItem(key);
      },
    },
    key: 'TicketingApp_ReactQueryCache',
    throttleTime: 1000,
  });
};

// Cleanup old databases
const cleanupOldDatabases = async () => {
  try {
    const databases = await indexedDB.databases();
    for (const db of databases) {
      if (db.name === 'TicketingCacheDB') {
        indexedDB.deleteDatabase(db.name);
        // Old database removed successfully
      }
    }
  } catch {
    // Failed to cleanup old databases - non-critical error
  }
};

// Global cache cleanup utility for logout only
export const clearAllCaches = async () => {
  try {
    localStorage.removeItem('TicketingApp_ReactQueryCache');
    await TanStackMessageCache.cleanupExpiredCache();
    // All caches cleared successfully on logout
  } catch {
    // Failed to clear caches - non-critical error
  }
};

// CRITICAL FIX: Utility to clean up stale ticket queries (404 tickets)
export const cleanupStaleTicketQueries = (
  queryClient: QueryClient,
  tenantId: string
) => {
  try {
    // Remove queries for tickets that returned 404
    const allQueries = queryClient.getQueriesData({ queryKey: [undefined] });

    allQueries.forEach(([queryKey, data]: [unknown, unknown]) => {
      if (
        Array.isArray(queryKey) &&
        queryKey[0] === 'tickets' &&
        queryKey[1] === tenantId &&
        (queryKey[2] === 'detail' || queryKey[3] === 'messages') &&
        (data === null || data === undefined)
      ) {
        const ticketId = queryKey[2] === 'detail' ? queryKey[3] : queryKey[3];
        console.log('🧹 Cleaning up stale query for ticket:', ticketId);
        queryClient.removeQueries({ queryKey });
      }
    });
  } catch (error) {
    console.warn('Failed to cleanup stale queries:', error);
  }
};

// Optimal 2025 React Query Provider with PersistQueryClientProvider
function ReactQueryProviderInner({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // CRITICAL FIX: Enable immediate background validation while maintaining cache-first approach
            staleTime: 0, // 2025 pattern: Always validate in background while serving cached data first
            gcTime: 60 * 60 * 1000, // 1 hour - keep cache alive for performance
            refetchOnWindowFocus: true, // Validate when switching back to tab
            refetchOnMount: 'always', // CRITICAL: Always validate on browser refresh/mount
            refetchOnReconnect: true, // Validate when network reconnects
            // Serve cached data immediately while validating in background
            placeholderData: (previousData: unknown) => previousData,
            retry: (
              failureCount,
              error: Error & { status?: number; code?: number }
            ) => {
              if (error?.status === 401 || error?.code === 401) {
                return false;
              }
              return failureCount < 2;
            },
            retryDelay: (attemptIndex) =>
              Math.min(500 * 2 ** attemptIndex, 5000),
            networkMode: 'online',
          },
          mutations: {
            retry: (
              failureCount,
              error: Error & { status?: number; code?: number }
            ) => {
              if (error?.status === 401 || error?.code === 401) {
                return false;
              }
              return failureCount < 1;
            },
            networkMode: 'online',
          },
        },
      })
  );

  const persister = createPersister();

  // Cleanup old databases on mount
  if (typeof window !== 'undefined') {
    cleanupOldDatabases();
  }

  return (
    <PersistQueryClientProvider
      client={queryClient}
      persistOptions={{
        persister,
        maxAge: 2 * 60 * 60 * 1000, // 2 hours - longer persistence for cache-first approach
        dehydrateOptions: {
          shouldDehydrateQuery: (query) => query.state.status === 'success',
        },
        // CRITICAL: Ensure cached data is served immediately while background validation occurs
      }}
    >
      {children}
    </PersistQueryClientProvider>
  );
}

// Main component that gets tenant context
export default function ReactQueryProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return <ReactQueryProviderInner>{children}</ReactQueryProviderInner>;
}
