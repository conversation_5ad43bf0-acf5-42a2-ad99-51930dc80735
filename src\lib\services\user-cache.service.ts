/**
 * Unified User Cache Service - 2025 Simplified
 *
 * Single source of truth for all user profile data across the application.
 * Implements cache-first strategy with React Query for persistence.
 *
 * Key Features:
 * - Single cache for user profiles (name, email, avatar)
 * - Cache-first: always check cache before fetching
 * - Automatic cache population from any data source
 * - Consistent data across real-time, API, and UI interactions
 */

import { QueryClient } from '@tanstack/react-query';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';
import { QueryKeys } from '@/lib/query-keys';

export interface CachedUserProfile {
  id: string;
  name: string;
  email: string;
  avatarUrl?: string;
  clerkId?: string;
  role?: string;
}

export class UserCacheService {
  private queryClient: QueryClient;
  private supabase: SupabaseClient<Database>;

  constructor(queryClient: QueryClient, supabase: SupabaseClient<Database>) {
    this.queryClient = queryClient;
    this.supabase = supabase;
  }

  /**
   * Get user profile with cache-first strategy
   * Returns cached data immediately if available, otherwise fetches and caches
   */
  async getUserProfile(
    userId: string,
    tenantId: string
  ): Promise<CachedUserProfile> {
    const cacheKey = QueryKeys.USERS.detail(tenantId, userId);

    console.log(`🔍 Looking up user profile in cache:`, {
      userId,
      tenantId,
      cacheKey,
    });

    // 1. Check React Query cache first
    const cachedData =
      this.queryClient.getQueryData<CachedUserProfile>(cacheKey);

    if (cachedData) {
      console.log(`✅ User ${userId} found in cache:`, cachedData.name, {
        email: cachedData.email,
        hasAvatar: !!cachedData.avatarUrl,
      });
      return cachedData;
    }

    // 2. If not in cache, fetch from database
    console.log(`⚠️ User ${userId} not in cache, fetching from database...`);
    const userProfile = await this.fetchUserProfile(userId);

    // 3. Cache the fetched data
    this.cacheUserProfile(userProfile, tenantId);

    return userProfile;
  }

  /**
   * Cache user profile data from any source
   * This ensures consistency across all data sources
   */
  cacheUserProfile(profile: CachedUserProfile, tenantId: string): void {
    const cacheKey = QueryKeys.USERS.detail(tenantId, profile.id);

    // Set in React Query cache with proper query key
    this.queryClient.setQueryData(cacheKey, profile);

    console.log(`💾 Cached user profile: ${profile.name} (${profile.id})`, {
      tenantId,
      cacheKey,
      email: profile.email,
      hasAvatar: !!profile.avatarUrl,
    });
  }

  /**
   * Batch cache multiple user profiles
   * Useful when processing ticket lists or real-time events
   */
  cacheUserProfiles(profiles: CachedUserProfile[], tenantId: string): void {
    profiles.forEach((profile) => {
      this.cacheUserProfile(profile, tenantId);
    });
  }

  /**
   * Extract and cache user data from ticket data
   * This populates the cache from existing ticket responses
   */
  extractAndCacheFromTicket(
    ticket: Record<string, unknown>,
    tenantId: string
  ): void {
    // Cache ticket creator
    if (ticket.userId && ticket.userName) {
      const profile: CachedUserProfile = {
        id: ticket.userId as string,
        name: ticket.userName as string,
        email: ticket.userEmail as string,
        role: 'user',
      };
      if (ticket.userAvatar) {
        profile.avatarUrl = ticket.userAvatar as string;
      }
      if (ticket.creatorClerkId) {
        profile.clerkId = ticket.creatorClerkId as string;
      }
      this.cacheUserProfile(profile, tenantId);
    }

    // Cache assigned user if present in metadata
    const metadata = ticket.metadata as Record<string, unknown> | undefined;
    if (ticket.assignedTo && metadata?.assignedUser) {
      const assignedUser = metadata.assignedUser as {
        name: string;
        email: string;
        avatar?: string;
        role?: string;
      };
      const assignedProfile: CachedUserProfile = {
        id: ticket.assignedTo as string,
        name: assignedUser.name,
        email: assignedUser.email,
        role: assignedUser.role || 'agent',
      };
      if (assignedUser.avatar) {
        assignedProfile.avatarUrl = assignedUser.avatar;
      }
      if (ticket.assignedToClerkId) {
        assignedProfile.clerkId = ticket.assignedToClerkId as string;
      }
      this.cacheUserProfile(assignedProfile, tenantId);
    }
  }

  /**
   * Extract and cache user data from message data
   */
  extractAndCacheFromMessage(
    message: Record<string, unknown>,
    tenantId: string
  ): void {
    if (message.authorId && message.authorName) {
      const messageProfile: CachedUserProfile = {
        id: message.authorId as string,
        name: message.authorName as string,
        email: '', // Email not available in messages
        role: 'user',
      };
      if (message.authorAvatar) {
        messageProfile.avatarUrl = message.authorAvatar as string;
      }
      this.cacheUserProfile(messageProfile, tenantId);
    }
  }

  /**
   * Fetch user profile from database
   * Only called when data is not in cache
   */
  private async fetchUserProfile(userId: string): Promise<CachedUserProfile> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('id, first_name, last_name, email, avatar_url, clerk_id, role')
        .eq('id', userId)
        .single();

      if (error || !data) {
        console.warn(`⚠️ User ${userId} not found in database`);
        return {
          id: userId,
          name: 'Unknown User',
          email: '<EMAIL>',
          role: 'user',
        };
      }

      const userProfile: CachedUserProfile = {
        id: data.id,
        name:
          `${data.first_name || ''} ${data.last_name || ''}`.trim() ||
          'Unknown User',
        email: data.email,
        role: data.role || 'user',
      };
      if (data.avatar_url) {
        userProfile.avatarUrl = data.avatar_url;
      }
      if (data.clerk_id) {
        userProfile.clerkId = data.clerk_id;
      }
      return userProfile;
    } catch (error) {
      console.error(`❌ Error fetching user ${userId}:`, error);
      return {
        id: userId,
        name: 'Unknown User',
        email: '<EMAIL>',
        role: 'user',
      };
    }
  }

  /**
   * Invalidate user cache for a specific user
   * Useful when user data is updated
   */
  invalidateUser(userId: string, tenantId: string): void {
    this.queryClient.invalidateQueries({
      queryKey: QueryKeys.USERS.detail(tenantId, userId),
    });
  }

  /**
   * Invalidate all user caches for a tenant
   */
  invalidateAllUsers(tenantId: string): void {
    this.queryClient.invalidateQueries({
      queryKey: QueryKeys.USERS.all(tenantId),
    });
  }
}

// Singleton instance management
let userCacheInstance: UserCacheService | null = null;

export function getUserCacheService(
  queryClient: QueryClient,
  supabase: SupabaseClient<Database>
): UserCacheService {
  if (!userCacheInstance) {
    userCacheInstance = new UserCacheService(queryClient, supabase);
  }
  return userCacheInstance;
}
