# 🔄 React Query: Background Validation + Cache‑First UI

React Query supports exactly the "stale‑while‑revalidate" pattern:

- On mount (such as a browser refresh), cache data is delivered instantly.
- Since default `staleTime` is 0, a background refetch is triggered immediately to validate data.
- When new server data arrives, the cache is updated and UI re‑renders. ([TanStack][1], [TanStack][2])
- So you get instant UI response, and fresh data shortly after.

### Key settings in your query setup:

```ts
useQuery(['ticket', ticketId], fetchTicket(ticketId), {
  staleTime: 0, // data becomes stale immediately
  cacheTime: Infinity, // keep cache alive across refreshes
  refetchOnMount: 'always', // always validate on mount
  refetchOnWindowFocus: false,
  refetchOnReconnect: false,
});
```

- `refetchOnMount: 'always'` ensures background validation every time—even if a fresh query had mounted. ([TanStack][2], [TanStack][1])
- `cacheTime: Infinity` prevents garbage collection so cached data stays through refresh. Without it, React Query drops cache after default 5 min once unobserved. ([Stack Overflow][3], [Stack Overflow][4])

---

## 🧩 Ticket List + Replies + Meta-data Queries

- Use a separate `useQuery(['ticket-messages', ticketId])` for replies/messages, same query options.
- For metadata fields (status, priority, assignment, etc.) you can either embed in the single `ticket` object or split into sub‑queries if you want fine-grained control (e.g. `['ticket-status', id]`, `['ticket-detail', id]` etc.)—ensuring unique, deterministic keys to prevent stale mismatches. ([Asep Alazhari][5], [TanStack][6])

---

## ✅ After Mutation: Keep Cache In Sync

When a ticket is updated (e.g. via mutation):

- Use `onSuccess` to `invalidateQueries(['ticket', ticketId])` and optionally `refetchQueries(['ticket-messages', ticketId])` to ensure UI displays server-confirmed data.
- Alternatively, update cache manually via `queryClient.setQueryData()` for atomic updates. ([TanStack][6], [Asep Alazhari][5])

---

## 🧠 Optional: Zustand UI State Integration

Use Zustand to store UI-level flags (like “is offline”, “currently syncing”), or selection state. But keep **server state within React Query**—that’s its strength.

Typical pattern:

```ts
// React Query fetch/update flows → onSuccess sync to Zustand if needed:
const { data } = useQuery(['ticket', id], fetchTicket);
useEffect(() => {
  if (data) ticketStore.getState().setTicket(data);
}, [data]);
```

This allows global UI components (e.g., sidebar detail view) to respond quickly to query updates. ([codez.guru][7], [peerdh.com][8])

---

## 📋 Summary Table: Workflow Summary

| Step/Event                  | UI Behavior                       | Cache Behavior                                 |
| --------------------------- | --------------------------------- | ---------------------------------------------- |
| Browser refresh / mount     | Show cached data instantly        | `refetchOnMount: 'always'` triggers validation |
| background validation       | silent fetch, no blocking UI      | fetch updates stale cache if data changed      |
| Mutation (e.g. update)      | user sees immediate UI feedback   | invalidate and optionally refetch or set cache |
| Cache retention across time | maintain offline or refresh state | `cacheTime: Infinity` prevents garbage collect |

---

## 🎯 Tests / Validation Checklist

1. **Reload** the page with open React Query DevTools:
   - Confirm cached data appears immediately.
   - Observe status `isFetching: true` confirming background request.
   - Verify that once response arrives, cache updates and UI rerenders seamlessly.

2. **Update ticket metadata / send reply**:
   - After mutation, check `invalidateQueries()` triggers refetch and UI shows updated version.

3. **Simulate stale server data**:
   - Modify a reply/status externally; refresh browser; verify query revalidation syncs the new change.

4. **Edge case: offline or failure**:
   - If fetch fails, UI continues showing cached data and a retry or error state can be surfaced via notifications (not blocking UI).

---

## ✅ Suggested Code Blueprint

```ts
// Query client global config
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 0,
      cacheTime: Infinity,
      refetchOnMount: 'always',
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      retry: 1,
    },
  },
});

// Ticket component
const { data: ticket, isFetching } = useQuery(['ticket', id], fetchTicket);
const { data: replies } = useQuery(['ticket-messages', id], fetchMessages);

useEffect(() => {
  if (ticket) ticketStore.getState().setTicket(ticket);
}, [ticket]);

// Mutation example
const updateMutation = useMutation(updateTicketFn, {
  onSuccess: () => {
    queryClient.invalidateQueries(['ticket', id]);
    queryClient.invalidateQueries(['ticket-messages', id]);
  },
});
```

---

### ✅ In summary:

- Use **React Query’s stale‑while‑revalidate** defaults (staleTime: 0 + refetchOnMount) to achieve immediate load + silent validation.
- Set `cacheTime: Infinity` to survive browser refresh.
- Use **invalidateQueries** or manual cache updates after mutations.
- Keep **Zustand** for UI state or non-server state; remain persistent and fast.
- This combination ensures **immediate UI**, **up-to-date data**, and **no stale cache** after refresh.

## 🔄 What’s New & Emerging (2025)

1. **TanStack React Query v5** (current version you're using v5.83):
   - Default stale‑while‑revalidate behavior— serves cache instantly, then background refetch on mount. ([GitHub][1], [Stack Overflow][2])
   - `placeholderData(prev => prev)` in v5 replaces old `keepPreviousData`, helping maintain UI continuity during key changes. ([GitHub][1])

2. **React‑DB Collections and Query Sync Storage Persister** (you already have @tanstack/db‑collections and query‑sync‑storage‑persister):
   - You can now persist cache to IndexedDB or localStorage so queries survive browser refresh, and revalidate in background on next mount. This effectively fulfills your cache retention + validation requirement out of the box.

3. **Better integration patterns between Zustand and TanStack Query**:
   - React Query handles server state; Zustand should focus on UI local/global flags (e.g. `isSyncing`, selection, filters). ([codez.guru][3], [peerdh.com][4])
   - Avoid syncing query data into Zustand unless truly needed. Using `onSuccess` callbacks is discouraged in React Query v5 – use `useEffect` to push into Zustand only when necessary. ([Stack Overflow][5], [codez.guru][3])

---

## ✅ Recommended Modern Setup for Your Requirements

### A. **QueryClient Configuration (v5)**

```ts
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 0,
      cacheTime: Infinity,
      refetchOnMount: 'always', // ensures validation each refresh
      placeholderData: (prev) => prev, // keeps previous data during key changes
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      retry: 1,
    },
  },
});
```

- `refetchOnMount: 'always'` triggers background validation on browser refresh—even if cache exists. ([Stack Overflow][2], [GitHub][1])
- `cacheTime: Infinity` ensures data persists across refresh and isn’t garbage-collected.
- `placeholderData(prev => prev)` smooths transitions when keys change. ([GitHub][1])

### B. **Use Query Sync Storage Persister**

```ts
const persister = createSyncStoragePersister({
  storage: window.localStorage, // or IndexedDB via idb-keyval
});

persistQueryClient({
  queryClient,
  persister,
  maxAge: Infinity,
});
```

This ensures cached ticket and messages data survive browser refresh and then undergo **silent background revalidation** for freshness.

### C. **Query Structure for Tickets & Replies**

```ts
const ticketQuery = useQuery(['ticket', ticketId], fetchTicket, {
  /* defaultOptions */
});
const messagesQuery = useQuery(['ticket-messages', ticketId], fetchMessages);
```

- Both queries benefit from shared defaults—immediate UI, then silent revalidation.
- If metadata and replies are separate in your API, keep them as separate query keys.

### D. **Post-Mutation Cache Handling**

- Use React Query’s `invalidateQueries()` to selectively invalidate relevant keys (e.g. status change, new reply). ([TanStack][6])

```ts
onSuccess: () => {
  queryClient.invalidateQueries({ queryKey: ['ticket', ticketId] });
  queryClient.invalidateQueries({ queryKey: ['ticket-messages', ticketId] });
};
```

- Alternatively, manually update cache with `setQueryData()` for atomic edits.

### E. **Zustand Integration (UI State Only)**

```ts
const useSyncStore = create((set) => ({
  isSyncing: false,
  setSyncing: (flag) => set({ isSyncing: flag }),
}));
```

In your component:

```ts
const { data } = ticketQuery;
useEffect(() => {
  // optional: sync UI flags
  useSyncStore.getState().setSyncing(ticketQuery.isFetching);
}, [ticketQuery.isFetching]);
```

- Avoid syncing ticket data into Zustand unless you have specific UI selection needs. ([Stack Overflow][5], [codez.guru][3])

---

## 🧪 Pattern Summary for Immediate UI + Fresh Data

| Feature                               | Behavior                                                                                      |
| ------------------------------------- | --------------------------------------------------------------------------------------------- |
| **On browser refresh**                | Cached data appears instantly. Background revalidation kicks off because of `refetchOnMount`  |
| **Server mismatch detection**         | Query revalidation detects differences in metadata/replies and updates cache/UI appropriately |
| **Post mutation updates**             | `invalidateQueries()` ensures server changes are fetched and UI is updated                    |
| **Cache persistence across sessions** | `react-query-persist-client` + `sync-storage-persister` ensures state survives across reloads |
| **UI state vs. server state**         | Zustand used only for UI flags, not data syncing                                              |

---

## ✅ Why This is Up-to-Date (Mid‑2025)

- React Query v5 behavior includes `placeholderData(prev => prev)` as replacement for old `keepPreviousData` semantics. ([GitHub][1])
- `query-sync-storage-persister` combined with React Query v5 allows persistent offline cache + refresh revalidation automatically.
- PMNDRs (Zustand maintainers) recommend avoiding syncing React‑Query data into Zustand and instead focusing on UI state slices. ([Stack Overflow][5], [GitHub][7])

---

## 🧪 Suggested Tests / Validation

1. **With DevTools open**, refresh page:
   - Observe immediate cache load, then background request (`isFetching` true), followed by UI update if data changed.

2. **Simulate external change** (status/reply update):
   - Refresh; verify background revalidation pulls new data into UI.

3. **Submit a ticket update** (mutation):
   - Examine `invalidateQueries()` triggers and UI shows server-confirmed data.

4. **Check cache persistence**:
   - Refresh multiple times; confirm `cacheTime: Infinity` and persister maintain data, without stale persistence after validation.

---

Would you like full code examples of using **`@tanstack/query-sync-storage-persister`**, or a working sample integrating this with **Next.js + Supabase + TanStack DB Collections**? I can craft that for your Code-AI prompt!

[1]: https://github.com/TanStack/query/discussions/6460?utm_source=chatgpt.com 'keepPreviousData deprecated - what now? · TanStack query - GitHub'
[2]: https://stackoverflow.com/questions/67614543/react-query-usequery-is-caching-response?utm_source=chatgpt.com 'React-Query useQuery is caching response - Stack Overflow'
[3]: https://codez.guru/guides/zustand/lesson-22-combining-zustand-with-react-query-for-async-data/?utm_source=chatgpt.com 'Combining Zustand with React Query for Async Data'
[4]: https://peerdh.com/blogs/programming-insights/integrating-zustand-with-react-query-for-streamlined-state-management-in-a-typescript-e-commerce-application?utm_source=chatgpt.com 'Integrating Zustand With React Query For Streamlined State Management'
[5]: https://stackoverflow.com/questions/68690221/how-to-use-zustand-to-store-the-result-of-a-query?utm_source=chatgpt.com 'How to use zustand to store the result of a query'
[6]: https://tanstack.com/query/v4/docs/framework/react/guides/query-invalidation?utm_source=chatgpt.com 'Query Invalidation | TanStack Query React Docs'
[7]: https://github.com/pmndrs/zustand/discussions/2289?utm_source=chatgpt.com 'Best practice with swr/tanstack-query? · pmndrs zustand - GitHub'
