import { AutoAssignmentService } from '@/features/settings/services/auto-assignment.service';
import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { SupabaseClient } from '@supabase/supabase-js';
import { validateDepartmentWithFallback } from '@/features/departments/utils/department-validation';

// TypeScript interfaces for better type safety
// Note: ClerkSessionClaims interface moved inline where needed

interface UserRecord {
  id: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  role: string;
}

async function getTenantUuid(
  serviceSupabase: SupabaseClient,
  tenantParam: string
) {
  if (
    !tenantParam.match(
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    )
  ) {
    const { data: tenantData, error: tenantError } = await serviceSupabase
      .from('tenants')
      .select('id')
      .eq('subdomain', tenantParam)
      .single();

    if (tenantError || !tenantData) {
      throw new Error(`Tenant '${tenantParam}' not found`);
    }
    return tenantData.id;
  }
  return tenantParam;
}

async function validateUserAccess(
  serviceSupabase: SupabaseClient,
  userId: string,
  tenantUuid: string
) {
  const { data: userData, error: userError } = await serviceSupabase
    .from('users')
    .select(
      'id, tenant_id, role, status, first_name, last_name, email, avatar_url'
    )
    .eq('clerk_id', userId)
    .single();
  if (userError || !userData) {
    throw new Error('User not found');
  }
  if (userData.tenant_id !== tenantUuid) {
    throw new Error('Access denied to this tenant');
  }
  return userData;
}

export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const tenantParam = searchParams.get('tenant_id');
    if (!tenantParam) {
      return NextResponse.json(
        { error: 'tenant_id parameter is required' },
        { status: 400 }
      );
    }

    // Parse role-based filtering parameters
    const roleFilter = searchParams.get('role_filter'); // 'assigned', 'created', 'all'
    const statusFilter = searchParams.get('status'); // comma-separated status values

    // Get user role from Clerk JWT claims (organization role)
    const clerkRole = (sessionClaims as { o?: { rol?: string } })?.o?.rol;

    // Get user role from Clerk JWT claims (organization role) with database fallback
    const clerkUserRole = (() => {
      switch (clerkRole) {
        case 'org:super_admin':
          return 'super_admin';
        case 'org:admin':
          return 'admin';
        case 'org:agent':
          return 'agent';
        case 'org:member':
          return 'user';
        default:
          return null; // Will use database role as fallback
      }
    })();

    const serviceSupabase = createServiceSupabaseClient();
    const tenantUuid = await getTenantUuid(serviceSupabase, tenantParam);
    const userData = await validateUserAccess(
      serviceSupabase,
      userId,
      tenantUuid
    );

    // CRITICAL FIX: Use database role as fallback if Clerk role is not available
    const userRole = clerkUserRole || userData.role || 'user';

    // Build the base query
    let ticketsQuery = serviceSupabase
      .from('tickets')
      .select(
        `
        *,
        users!tickets_created_by_fkey (
          id,
          clerk_id,
          first_name,
          last_name,
          email,
          role,
          avatar_url
        ),
        assigned_user:users!tickets_assigned_to_fkey (
          id,
          clerk_id,
          first_name,
          last_name,
          email,
          role,
          avatar_url
        )
      `
      )
      .eq('tenant_id', tenantUuid);

    // Apply role-based filtering based on Clerk role
    // Use userData.id (database UUID) instead of userId (Clerk ID) for database queries
    const dbUserId = userData.id;

    switch (userRole) {
      case 'super_admin':
        // Super admin sees all tickets, can filter by assignment
        if (roleFilter === 'assigned') {
          ticketsQuery = ticketsQuery.eq('assigned_to', dbUserId);
        } else if (roleFilter === 'created') {
          ticketsQuery = ticketsQuery.eq('created_by', dbUserId);
        }
        // For 'all' or no filter, super admin sees everything (no additional filter)
        break;

      case 'admin':
        // CRITICAL FIX: Admin sees all tickets in their tenant, can filter by assignment/creation
        if (roleFilter === 'assigned') {
          ticketsQuery = ticketsQuery.eq('assigned_to', dbUserId);
        } else if (roleFilter === 'created') {
          ticketsQuery = ticketsQuery.eq('created_by', dbUserId);
        }
        // For 'all' or no filter, admin sees all tickets in tenant (no additional filtering)
        // This ensures Admin can see user-created tickets in "New Tickets" section
        break;

      case 'agent':
        // Agent sees all tickets but can filter by assignment/creation
        if (roleFilter === 'assigned') {
          ticketsQuery = ticketsQuery.eq('assigned_to', dbUserId);
        } else if (roleFilter === 'created') {
          ticketsQuery = ticketsQuery.eq('created_by', dbUserId);
        }
        // For 'all' or no filter, agent sees all tickets
        break;

      case 'user':
      default:
        // Users can only see tickets they created or are assigned to
        if (roleFilter === 'assigned') {
          ticketsQuery = ticketsQuery.eq('assigned_to', dbUserId);
        } else if (roleFilter === 'created') {
          ticketsQuery = ticketsQuery.eq('created_by', dbUserId);
        } else {
          // Default: show both created and assigned tickets
          ticketsQuery = ticketsQuery.or(
            `created_by.eq.${dbUserId},assigned_to.eq.${dbUserId}`
          );
        }
        break;
    }

    // Apply status filtering if provided
    if (statusFilter) {
      const statuses = statusFilter.split(',').map((s) => s.trim());
      ticketsQuery = ticketsQuery.in('status', statuses);
    }

    const { data: tickets, error: fetchError } = await ticketsQuery.order(
      'created_at',
      { ascending: false }
    );

    if (fetchError) {
      console.error('Failed to fetch tickets:', fetchError);
      return NextResponse.json(
        { error: `Failed to fetch tickets: ${fetchError.message}` },
        { status: 500 }
      );
    }

    // Fetch ticket-level attachments separately (only those not linked to specific messages)
    const ticketIds = (tickets || []).map((ticket) => ticket.id);
    const { data: ticketAttachments } = await serviceSupabase
      .from('attachments')
      .select('*')
      .in('ticket_id', ticketIds)
      .is('message_id', null)
      .eq('tenant_id', tenantUuid);

    // Group attachments by ticket_id
    const attachmentsByTicket = (ticketAttachments || []).reduce(
      (acc, att) => {
        const ticketId = att.ticket_id;
        if (ticketId) {
          if (!acc[ticketId]) {
            acc[ticketId] = [];
          }
          acc[ticketId].push(att);
        }
        return acc;
      },
      {} as Record<
        string,
        Array<{
          id: string;
          file_name: string;
          file_type: string;
          file_size: number;
          created_at: string | null;
        }>
      >
    );

    // Helper function to safely access metadata
    const getAssignmentMetadata = (metadata: unknown) => {
      if (metadata && typeof metadata === 'object') {
        const metaObj = metadata as Record<string, unknown>;
        if (metaObj.assignment && typeof metaObj.assignment === 'object') {
          return metaObj.assignment as Record<string, unknown>;
        }
      }
      return null;
    };

    // Helper function to get creator Clerk ID from metadata
    const getCreatorClerkId = (metadata: unknown): string | null => {
      if (metadata && typeof metadata === 'object') {
        const metaObj = metadata as Record<string, unknown>;
        return (metaObj.creator_clerk_id as string) || null;
      }
      return null;
    };

    // Get unique assigned_by user IDs from assignment metadata
    const assignedByUserIds = new Set<string>();
    (tickets || []).forEach((ticket) => {
      const assignmentMeta = getAssignmentMetadata(ticket.metadata);
      if (assignmentMeta?.assigned_by) {
        assignedByUserIds.add(assignmentMeta.assigned_by as string);
      }
    });

    // Fetch assigned_by user information if we have any
    let assignedByUsers: Record<string, UserRecord> = {};
    if (assignedByUserIds.size > 0) {
      const { data: assignedByUsersData, error: assignedByUsersError } =
        await serviceSupabase
          .from('users')
          .select('id, first_name, last_name, email, role, avatar_url')
          .in('id', Array.from(assignedByUserIds));

      if (assignedByUsersError) {
        console.error('Error fetching assignedByUsers:', assignedByUsersError);
      }

      if (assignedByUsersData) {
        assignedByUsers = assignedByUsersData.reduce(
          (acc, user) => {
            acc[user.id] = user;
            return acc;
          },
          {} as Record<string, UserRecord>
        );
      }
    }

    const transformedTickets = (tickets || []).map((ticket) => {
      const assignmentMeta = getAssignmentMetadata(ticket.metadata);

      return {
        id: ticket.id,
        tenantId: tenantParam,
        title: ticket.title,
        description: ticket.description,
        status: ticket.status,
        priority: ticket.priority,
        department: ticket.department,
        createdAt: new Date(ticket.created_at!),
        updatedAt: new Date(ticket.updated_at!),
        userId: ticket.created_by,
        creatorClerkId: getCreatorClerkId(ticket.metadata), // Add Clerk user ID for frontend filtering
        userName:
          `${ticket.users?.first_name || ''} ${
            ticket.users?.last_name || ''
          }`.trim() || 'Unknown User',
        userEmail: ticket.users?.email || '<EMAIL>',
        userAvatar: ticket.users?.avatar_url || undefined,
        messages: [],
        attachments: (attachmentsByTicket[ticket.id] || []).map((att) => ({
          id: att.id,
          name: att.file_name,
          type: att.file_type,
          size: att.file_size,
          url: `/api/attachments/${att.id}`,
          uploadedAt: new Date(att.created_at || new Date().toISOString()),
        })),
        // Assignment tracking fields
        assignedTo: ticket.assigned_to,
        assignedToClerkId: ticket.assigned_user?.clerk_id || null, // Add Clerk user ID for frontend filtering
        assignedBy: assignmentMeta?.assigned_by as string,
        assignedByClerkId:
          (assignmentMeta?.assigned_by_clerk_id as string) || null, // Add Clerk user ID for frontend filtering
        assignedAt: assignmentMeta?.assigned_at
          ? new Date(assignmentMeta.assigned_at as string)
          : ticket.assigned_to
            ? new Date(ticket.updated_at!)
            : undefined,
        // Additional metadata fields
        dueDate: ticket.due_date ? new Date(ticket.due_date) : undefined,
        resolvedAt: ticket.resolved_at
          ? new Date(ticket.resolved_at)
          : undefined,
        closedAt: ticket.closed_at ? new Date(ticket.closed_at) : undefined,
        tags: ticket.tags || [],
        metadata: {
          ...(ticket.metadata && typeof ticket.metadata === 'object'
            ? (ticket.metadata as Record<string, unknown>)
            : {}),
          // Add assignment metadata
          assignedUser: ticket.assigned_user
            ? {
                id: ticket.assigned_user.id,
                name:
                  `${ticket.assigned_user.first_name || ''} ${
                    ticket.assigned_user.last_name || ''
                  }`.trim() || 'Unknown User',
                email: ticket.assigned_user.email,
                role: ticket.assigned_user.role,
                avatar: ticket.assigned_user.avatar_url,
              }
            : undefined,
          createdByUser: {
            id: ticket.users?.id,
            name:
              `${ticket.users?.first_name || ''} ${
                ticket.users?.last_name || ''
              }`.trim() || 'Unknown User',
            email: ticket.users?.email || '<EMAIL>',
            role: ticket.users?.role,
          },
          // Add assignment information for proper display with actual user names
          assignment: assignmentMeta
            ? {
                assignedByUser: (() => {
                  const assignedByUserId = assignmentMeta.assigned_by as string;
                  const assignedByUser = assignedByUsers[assignedByUserId];

                  // CRITICAL FIX: Ensure proper fallback with actual user data
                  if (assignedByUser) {
                    return {
                      id: assignedByUser.id,
                      name:
                        `${assignedByUser.first_name || ''} ${
                          assignedByUser.last_name || ''
                        }`.trim() || 'Unknown User',
                      email: assignedByUser.email,
                      role: assignedByUser.role,
                    };
                  }

                  // Enhanced fallback: Try to get user info from the current context
                  // This handles cases where the assignedByUser lookup fails
                  return {
                    id: assignedByUserId,
                    name: 'System', // Fallback name
                    role: assignmentMeta.assigned_by_role || 'system',
                  };
                })(),
                assignedAt: assignmentMeta.assigned_at,
                reason: assignmentMeta.reason,
                auto_assigned: assignmentMeta.auto_assigned || false,
                assignment_reason: assignmentMeta.assignment_reason,
                assigned_at: assignmentMeta.assigned_at, // Ensure this field is included
              }
            : undefined,
        },
      };
    });

    return NextResponse.json(transformedTickets, { status: 200 });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Internal server error';
    console.error('Unexpected error in GET /api/tickets:', error);
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

const CreateTicketSchema = z.object({
  title: z.string().min(1).max(200).trim(),
  description: z.string().min(1).max(5000).trim(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  department: z
    .string()
    .min(1, 'Department is required')
    .max(50, 'Department name too long'),
  tenant_id: z.string().min(1),
  assigned_to: z.string().uuid().optional().nullable(),
  cc: z.array(z.string().uuid()).optional().default([]),
  attachment_ids: z.array(z.string().uuid()).optional().default([]),
});

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const requestBody = await request.json();

    const validationResult = CreateTicketSchema.safeParse(requestBody);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid ticket data',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const ticketData = validationResult.data;

    const serviceSupabase = createServiceSupabaseClient();
    const tenantUuid = await getTenantUuid(
      serviceSupabase,
      ticketData.tenant_id
    );
    const userData = await validateUserAccess(
      serviceSupabase,
      userId,
      tenantUuid
    );

    if (userData.status !== 'active') {
      return NextResponse.json(
        { error: 'Account is not active' },
        { status: 403 }
      );
    }

    // Check if user has permission to create tickets
    if (!['admin', 'super_admin', 'user'].includes(userData.role)) {
      return NextResponse.json(
        {
          error:
            'Insufficient permissions to create tickets. Agents cannot create tickets.',
        },
        { status: 403 }
      );
    }

    // Validate department exists and is active for this tenant
    const departmentValidation = await validateDepartmentWithFallback(
      tenantUuid,
      ticketData.department
    );

    if (!departmentValidation.isValid) {
      return NextResponse.json(
        {
          error: departmentValidation.error,
          suggestions: departmentValidation.suggestions,
        },
        { status: 400 }
      );
    }

    // Determine assignment using auto-assignment rules
    let finalAssignedTo = ticketData.assigned_to || null;
    let assignmentMetadata = null;

    // Only apply auto-assignment if no agent was manually assigned
    if (!ticketData.assigned_to) {
      const autoAssignmentService = new AutoAssignmentService(serviceSupabase);
      const assignmentResult =
        await autoAssignmentService.getAssignmentForTicket(
          tenantUuid,
          ticketData.department
        );

      if (assignmentResult.assigned_agent_id) {
        finalAssignedTo = assignmentResult.assigned_agent_id;
        assignmentMetadata = autoAssignmentService.createAssignmentMetadata(
          assignmentResult,
          userData.id,
          userId,
          userData.role
        );
      }
    } else {
      // Manual assignment metadata
      assignmentMetadata = {
        assigned_by: userData.id,
        assigned_by_clerk_id: userId,
        assigned_at: new Date().toISOString(),
        assigned_by_role: userData.role || 'admin',
        assignment_reason: 'manual',
        auto_assigned: false,
      };
    }

    // Get assigned user's Clerk ID if there's an assignment
    let assignedToClerkId = null;
    if (finalAssignedTo) {
      const { data: assignedUser } = await serviceSupabase
        .from('users')
        .select('clerk_id')
        .eq('id', finalAssignedTo)
        .single();

      if (assignedUser) {
        assignedToClerkId = assignedUser.clerk_id;
      }
    }

    // Convert department name to lowercase for database constraint
    // The database constraint expects enum values like 'support', 'sales', etc.
    const departmentForDb = ticketData.department.toLowerCase();

    const newTicketData = {
      title: ticketData.title,
      description: ticketData.description,
      priority: ticketData.priority,
      department: departmentForDb, // Use lowercase department name for database constraint
      tenant_id: tenantUuid,
      created_by: userData.id,
      // CORRECT STATUS LOGIC: All newly created tickets start as 'new'
      // Status changes to 'open' only when assigned agent actually opens the ticket
      status: 'new' as const,
      assigned_to: finalAssignedTo,
      assigned_to_clerk_id: assignedToClerkId, // CRITICAL FIX: Store assignee's Clerk ID for role-based filtering
      metadata: {
        cc: ticketData.cc,
        created_via: 'web_form',
        user_agent: request.headers.get('user-agent') || 'unknown',
        creator_role: userData.role, // Track creator's role for filtering
        creator_clerk_id: userId, // Track creator's Clerk ID for frontend filtering
        // Assignment tracking metadata
        ...(finalAssignedTo &&
          assignmentMetadata && {
            assignment: assignmentMetadata,
          }),
      },
    };

    const { data: newTicket, error: insertError } = await serviceSupabase
      .from('tickets')
      .insert(newTicketData)
      .select('*')
      .single();

    if (insertError) {
      console.error('Database insertion error:', insertError);
      if (insertError.code === '23505') {
        return NextResponse.json(
          { error: 'Duplicate ticket detected' },
          { status: 409 }
        );
      }
      return NextResponse.json(
        { error: 'Failed to create ticket' },
        { status: 500 }
      );
    }

    // Link attachments to the ticket if any were provided
    if (ticketData.attachment_ids && ticketData.attachment_ids.length > 0) {
      const { error: attachmentError } = await serviceSupabase
        .from('attachments')
        .update({ ticket_id: newTicket.id })
        .in('id', ticketData.attachment_ids)
        .eq('tenant_id', tenantUuid)
        .eq('uploaded_by', userData.id);

      if (attachmentError) {
        console.error('Failed to link attachments:', attachmentError);
        // Don't fail the ticket creation, just log the error
      }
    }

    // Fetch assigned user information if ticket was assigned
    let assignedUserData = null;
    if (finalAssignedTo) {
      const { data: assignedUser, error: assignedUserError } =
        await serviceSupabase
          .from('users')
          .select(
            'id, first_name, last_name, email, role, clerk_id, avatar_url'
          )
          .eq('id', finalAssignedTo)
          .single();

      if (assignedUserError) {
        console.error('Error fetching assigned user:', assignedUserError);
      }

      if (assignedUser) {
        assignedUserData = {
          id: assignedUser.id,
          name:
            `${assignedUser.first_name || ''} ${assignedUser.last_name || ''}`.trim() ||
            'Unknown User',
          email: assignedUser.email,
          role: assignedUser.role,
          avatar: assignedUser.avatar_url,
        };
      }
    }

    // Create complete transformed ticket for optimistic updates
    // Include all metadata that matches post-refresh behavior
    const transformedTicket = {
      id: newTicket.id,
      tenantId: ticketData.tenant_id,
      title: newTicket.title,
      description: newTicket.description,
      status: newTicket.status,
      priority: newTicket.priority,
      department: newTicket.department,
      createdAt: new Date(newTicket.created_at!),
      updatedAt: new Date(newTicket.updated_at!),
      userId: newTicket.created_by,
      creatorClerkId: userId, // Use the current user's Clerk ID directly
      userName:
        `${userData.first_name || ''} ${userData.last_name || ''}`.trim() ||
        'Unknown User',
      userEmail: userData.email || '<EMAIL>',
      userAvatar: userData.avatar_url || undefined,
      messages: [],
      attachments: [],
      // Assignment tracking fields
      assignedTo: newTicket.assigned_to,
      assignedToClerkId: assignedToClerkId, // CRITICAL FIX: Include assignee's Clerk ID for immediate filtering
      assignedBy: assignmentMetadata?.assigned_by as string,
      assignedByClerkId:
        (assignmentMetadata?.assigned_by_clerk_id as string) || null,
      assignedAt: assignmentMetadata?.assigned_at
        ? new Date(assignmentMetadata.assigned_at as string)
        : undefined,
      // Additional metadata fields
      dueDate: newTicket.due_date ? new Date(newTicket.due_date) : undefined,
      resolvedAt: undefined,
      closedAt: undefined,
      tags: newTicket.tags || [],
      metadata: {
        ...(newTicket.metadata && typeof newTicket.metadata === 'object'
          ? (newTicket.metadata as Record<string, unknown>)
          : {}),
        // Add assignment metadata for proper recipient display
        assignedUser: assignedUserData,
        createdByUser: {
          id: userData.id,
          name:
            `${userData.first_name || ''} ${userData.last_name || ''}`.trim() ||
            'Unknown User',
          email: userData.email || '<EMAIL>',
          role: userData.role,
        },
        // Add assignment information for proper display
        assignment: assignmentMetadata
          ? {
              assignedByUser: {
                id: userData.id,
                name:
                  `${userData.first_name || ''} ${userData.last_name || ''}`.trim() ||
                  'Unknown User',
                email: userData.email || '<EMAIL>',
                role: userData.role,
              },
              assignedAt: assignmentMetadata.assigned_at,
              reason: assignmentMetadata.assignment_reason,
              auto_assigned: assignmentMetadata.auto_assigned || false,
            }
          : undefined,
      },
    };

    return NextResponse.json(
      {
        success: true,
        message: 'Ticket created successfully',
        ticket: transformedTicket,
      },
      { status: 201 }
    );
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Internal server error';
    console.error('Unexpected error in ticket creation:', error);
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
