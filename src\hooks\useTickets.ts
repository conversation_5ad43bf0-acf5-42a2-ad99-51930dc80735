/**
 * Custom React Query Hooks for Tickets - 2025 Optimized
 *
 * These hooks abstract all data-fetching logic and reduce component code by 90%
 * Following TkDodo's best practices for React Query v5
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ticketQueryOptions } from '@/lib/query-options';
import { QueryKeys, CACHE_CONFIG } from '@/lib/query-keys';
import type {
  Ticket,
  TicketMessage,
} from '@/features/ticketing/models/ticket.schema';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useUserDatabaseId } from '@/features/shared/hooks/useUserDatabaseId';
import { useTenantUuid } from '@/hooks/useRealtimeQuery';
import { useUnifiedRealtimeSubscription } from './useUnifiedRealtimeSubscription';
import { toast } from '@/features/shared/components/toast';
import {
  usePopulateUserCacheFromTickets,
  usePopulateUserCacheFromMessages,
} from './useUserCache';

interface RoleBasedFilterContext {
  tenantId: string;
  role: string;
  userId?: string;
}

interface TicketFilterOptions {
  status?: string[];
  priority?: string[];
  roleFilter?: 'new' | 'assigned' | 'all';
}

interface CreateTicketData {
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  department: string;
  assigned_to?: string;
  assignedTo?: string; // Support both camelCase and snake_case
  assigned_to_clerk_id?: string;
  assignedToClerkId?: string; // Support both camelCase and snake_case
  cc?: string[];
  attachment_ids?: string[];
}

interface UpdateTicketData {
  title?: string;
  description?: string;
  status?: string;
  priority?: string;
  assigned_to?: string;
}

// Enhanced hook for ticket list with 2025 cache-first strategy
export const useTickets = (
  context: RoleBasedFilterContext,
  options?: TicketFilterOptions & { enabled?: boolean }
) => {
  const { enabled = true, ...filterOptions } = options || {};

  const query = useQuery({
    ...ticketQueryOptions.list(context, filterOptions),
    // Apply cache-first configuration for instant loading
    ...CACHE_CONFIG.tickets,
    enabled,
  });

  // Populate user cache from ticket data
  usePopulateUserCacheFromTickets(query.data, context.tenantId);

  return query;
};

// Custom hook for ticket detail with select optimization
export const useTicket = (
  tenantId: string,
  ticketId: string,
  enabled = true
) => {
  // CRITICAL FIX: Prevent API calls for optimistic tickets
  const isOptimisticTicket =
    ticketId?.startsWith('optimistic-') || ticketId?.startsWith('temp-');

  return useQuery({
    ...ticketQueryOptions.detail(tenantId, ticketId),
    enabled: enabled && !!tenantId && !!ticketId && !isOptimisticTicket,
  });
};

// Real-time version of useTicket for ticket detail page with unified subscription
export const useRealtimeTicket = (
  tenantId: string,
  ticketId: string,
  enabled = true
) => {
  const { isLoggingOut } = useAuth();

  // CRITICAL FIX: Prevent API calls for optimistic tickets
  const isOptimisticTicket =
    ticketId?.startsWith('optimistic-') || ticketId?.startsWith('temp-');

  // Resolve tenant UUID first
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  // Use standard React Query without real-time subscription (handled by unified manager)
  const query = useQuery({
    queryKey: QueryKeys.TICKETS.detail(tenantId, ticketId),
    queryFn: async () => {
      if (!tenantUuid) {
        throw new Error('Tenant UUID not resolved');
      }

      console.log(
        '🔍 Fetching ticket detail for ticket:',
        ticketId,
        'tenant UUID:',
        tenantUuid
      );

      const response = await fetch(
        `/api/tickets/${ticketId}?tenant_id=${tenantUuid}`
      );

      if (!response.ok) {
        if (response.status === 404) {
          console.log('🗑️ Ticket not found (404), returning null:', ticketId);
          // Don't throw for 404s - just return null to indicate ticket doesn't exist
          // This is expected behavior when a ticket is deleted
          return null;
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to fetch ticket');
      }

      const data = await response.json();
      return data.data;
    },
    enabled:
      enabled &&
      !!tenantId &&
      !!ticketId &&
      !isOptimisticTicket &&
      !!tenantUuid &&
      !isLoggingOut,
    // CRITICAL FIX: Configure retry behavior to avoid hammering non-existent tickets
    retry: (failureCount, error) => {
      // Don't retry 404 errors - ticket doesn't exist
      if (error.message.includes('404')) {
        console.warn('🚫 Not retrying 404 for ticket:', ticketId);
        return false;
      }
      // Retry other errors up to 2 times
      return failureCount < 2;
    },
    ...CACHE_CONFIG.tickets,
  });

  // Use unified real-time subscription for automatic cache updates
  useUnifiedRealtimeSubscription(tenantId);

  return query;
};

// REMOVED: useGlobalMessageRealtime - replaced by unified subscription manager

// Custom hook for ticket messages with real-time updates
export const useTicketMessages = (
  tenantId: string,
  ticketId: string,
  enabled = true
) => {
  const { isLoggingOut } = useAuth();

  // CRITICAL FIX: Prevent API calls for optimistic tickets
  const isOptimisticTicket =
    ticketId?.startsWith('optimistic-') || ticketId?.startsWith('temp-');

  // CRITICAL FIX: Resolve tenant UUID for proper real-time subscription
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  // Use unified real-time subscription for automatic cache updates
  useUnifiedRealtimeSubscription(tenantId);

  // Use regular query (not real-time) since unified subscription handles real-time updates
  const query = useQuery({
    queryKey: QueryKeys.TICKETS.messages(tenantUuid || tenantId, ticketId),
    queryFn: async () => {
      if (!tenantUuid) {
        throw new Error('Tenant UUID not resolved');
      }

      console.log(
        '🔍 Fetching messages for ticket:',
        ticketId,
        'tenant UUID:',
        tenantUuid
      );

      const response = await fetch(
        `/api/tickets/${ticketId}/messages?tenant_id=${tenantUuid}`
      );

      if (!response.ok) {
        if (response.status === 404) {
          console.log(
            '🗑️ Ticket messages not found (404), returning empty array:',
            ticketId
          );
          // Return empty array for non-existent tickets
          // This is expected behavior when a ticket is deleted
          return [];
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to fetch messages');
      }

      const data = await response.json();
      return data.messages || [];
    },
    enabled:
      enabled &&
      !!tenantId &&
      !!ticketId &&
      !isOptimisticTicket &&
      !!tenantUuid &&
      !isLoggingOut,
    ...CACHE_CONFIG.messages,
  });

  // Populate user cache from message data
  usePopulateUserCacheFromMessages(query.data, tenantId);

  return query;
};

// Custom mutation hook for opening tickets with optimistic updates
export const useOpenTicket = (tenantId: string) => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // CRITICAL FIX: Resolve tenant UUID for proper query key matching
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  return useMutation({
    mutationFn: async (ticketId: string) => {
      // Get tenant ID from the current subdomain
      const hostname = window.location.hostname;
      const parts = hostname.split('.');
      const currentTenantId = parts.length > 1 ? parts[0] : null;

      if (
        !currentTenantId ||
        currentTenantId === 'localhost' ||
        currentTenantId === 'www'
      ) {
        throw new Error('Tenant ID not available');
      }

      const response = await fetch(`/api/tickets/${ticketId}/open`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenant_id: currentTenantId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return response.json();
    },
    onMutate: async (ticketId: string) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QueryKeys.TICKETS.detail(tenantId, ticketId),
      });

      // CRITICAL FIX: Snapshot previous value (could be single ticket or array)
      const previousTicket = queryClient.getQueryData(
        QueryKeys.TICKETS.detail(tenantId, ticketId)
      );

      // CRITICAL FIX: Optimistically update the ticket status to 'open' as array to match useRealtimeQuery format
      queryClient.setQueryData(
        QueryKeys.TICKETS.detail(tenantId, ticketId),
        (old: Ticket | Ticket[] | undefined) => {
          // Handle both single ticket and array formats for compatibility
          const currentTicket = Array.isArray(old) ? old[0] : old;
          if (!currentTicket) return old;

          // CRITICAL FIX: Preserve all existing metadata, especially assignment information
          const existingMetadata =
            (currentTicket.metadata as Record<string, unknown>) || {};

          const updatedTicket = {
            ...currentTicket,
            status: 'open' as const,
            updatedAt: new Date(),
            metadata: {
              ...existingMetadata,
              // Add opening metadata without overwriting assignment data
              opening: {
                opened_by: user?.id,
                opened_at: new Date().toISOString(),
                opened_by_role: user?.publicMetadata?.role || 'agent',
              },
            },
          };

          // Always return as array to match useRealtimeQuery expectations
          return [updatedTicket];
        }
      );

      // CRITICAL FIX: Update all possible ticket list query keys to ensure immediate UI updates
      // This covers all the different query key patterns used across the app
      const queryKeysToUpdate = [
        ['tickets', tenantId, 'list'],
        ['tickets', tenantUuid, 'list'],
        ['realtime-tickets', tenantId],
        ['realtime-tickets', tenantUuid],
      ];

      queryKeysToUpdate.forEach((queryKeyPattern) => {
        queryClient.setQueriesData(
          {
            queryKey: queryKeyPattern,
            exact: false,
          },
          (old: Ticket[] | undefined) => {
            if (!old) return old;
            return old.map((ticket) =>
              ticket.id === ticketId
                ? {
                    ...ticket,
                    status: 'open' as const,
                    updatedAt: new Date(),
                    // CRITICAL FIX: Preserve existing metadata including assignment information
                    metadata: {
                      ...((ticket.metadata as Record<string, unknown>) || {}),
                      opening: {
                        opened_by: user?.id,
                        opened_at: new Date().toISOString(),
                        opened_by_role: user?.publicMetadata?.role || 'agent',
                      },
                    },
                  }
                : ticket
            );
          }
        );
      });

      return { previousTicket };
    },
    onError: (_err, ticketId, context) => {
      // CRITICAL FIX: Rollback on error with proper array format
      if (context?.previousTicket) {
        // Ensure rollback data is in array format to match useRealtimeQuery expectations
        const rollbackData = Array.isArray(context.previousTicket)
          ? context.previousTicket
          : [context.previousTicket];
        queryClient.setQueryData(
          QueryKeys.TICKETS.detail(tenantId, ticketId),
          rollbackData
        );
      }
    },
    onSettled: (_data, error, ticketId) => {
      // CRITICAL FIX: Only invalidate on error to prevent unnecessary skeleton loading
      // Successful optimistic ticket opening should remain stable without refetching
      if (error) {
        const currentTenantUuid = tenantUuid || tenantId;
        queryClient.invalidateQueries({
          queryKey: QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
        });
        queryClient.invalidateQueries({
          queryKey: ['tickets', currentTenantUuid, 'list'],
          exact: false,
        });
      }
      // On success, the optimistic update is already in place and should remain
    },
  });
};

// Custom mutation hook with optimistic updates
export const useCreateTicket = (
  tenantId: string,
  onOptimisticTicketCreated?: (ticketId: string) => void,
  onRealTicketCreated?: (
    realTicketId: string,
    optimisticTicketId: string
  ) => void
) => {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { userDatabaseId } = useUserDatabaseId();

  // CRITICAL FIX: Resolve tenant UUID for proper query key matching
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  return useMutation({
    mutationFn: async (ticketData: CreateTicketData) => {
      const response = await fetch('/api/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...ticketData,
          tenant_id: tenantId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to create ticket');
      }

      return response.json();
    },
    onMutate: async (newTicket) => {
      // CRITICAL FIX: Use tenant UUID for proper query key matching
      const currentTenantUuid = tenantUuid || tenantId;

      // Cancel outgoing refetches for all ticket list queries
      await queryClient.cancelQueries({
        queryKey: ['tickets', currentTenantUuid, 'list'],
        exact: false,
      });

      // Snapshot previous values for all ticket list queries
      const previousTicketsData = new Map();
      queryClient
        .getQueriesData({
          queryKey: ['tickets', currentTenantUuid, 'list'],
          exact: false,
        })
        .forEach(([queryKey, data]) => {
          previousTicketsData.set(queryKey, data);
        });

      // Create optimistic ticket with unique ID and proper assignment data
      // Use crypto.randomUUID for stable client-side ID generation
      const optimisticId =
        typeof window !== 'undefined' && window.crypto?.randomUUID
          ? `optimistic-${window.crypto.randomUUID()}`
          : `optimistic-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      const optimisticTicket = {
        ...newTicket,
        id: optimisticId,
        tenantId: currentTenantUuid,
        createdAt: new Date(),
        updatedAt: new Date(),
        // CRITICAL FIX: Use database UUID for userId to match filtering logic
        userId: userDatabaseId || user?.id || 'temp-user',
        // CRITICAL FIX: Add creatorClerkId for fallback matching in role-based filtering
        creatorClerkId: user?.id || '',
        userName: user
          ? `${user.firstName || ''} ${user.lastName || ''}`.trim() ||
            'Unknown User'
          : 'Creating...',
        userEmail: user?.primaryEmailAddress?.emailAddress || '',
        messages: [],
        attachments: [],
        status: 'new',
        tags: [],
        metadata: {},
        // CRITICAL FIX: Include assignment data in optimistic update to prevent timing issues
        // Handle both camelCase (frontend) and snake_case (API) field names
        assignedTo: newTicket.assignedTo || newTicket.assigned_to || undefined,
        assignedToClerkId:
          newTicket.assignedToClerkId ||
          newTicket.assigned_to_clerk_id ||
          undefined,
        // Additional assignment tracking fields for consistency
        assignedBy: undefined,
        assignedByClerkId: undefined,
        assignedAt:
          newTicket.assignedTo || newTicket.assigned_to
            ? new Date()
            : undefined,
      } as Ticket;

      // CRITICAL FIX: Update all ticket list queries with correct tenant UUID
      // This matches the query key pattern used by useRealtimeTickets
      queryClient.setQueriesData(
        {
          queryKey: ['tickets', currentTenantUuid, 'list'],
          exact: false,
        },
        (old: Ticket[] | undefined) => {
          if (!old) return [optimisticTicket];
          return [optimisticTicket, ...old];
        }
      );

      // CRITICAL FIX: Immediately select the optimistic ticket for instant UI feedback
      if (onOptimisticTicketCreated) {
        onOptimisticTicketCreated(optimisticTicket.id);
      }

      return { previousTicketsData, optimisticTicket };
    },
    onSuccess: (result, _variables, context) => {
      // CRITICAL FIX: Replace optimistic ticket with real ticket data in cache
      // This maintains the ticket position in the list while updating with real data
      const realTicket = result.ticket as Ticket;
      const optimisticTicket = context?.optimisticTicket;

      if (optimisticTicket && realTicket) {
        const currentTenantUuid = tenantUuid || tenantId;

        // Update all ticket list queries to replace optimistic ticket with real ticket
        queryClient.setQueriesData(
          {
            queryKey: ['tickets', currentTenantUuid, 'list'],
            exact: false,
          },
          (old: Ticket[] | undefined) => {
            if (!old) return old;

            // Replace optimistic ticket with real ticket, maintaining position
            return old.map((ticket) =>
              ticket.id === optimisticTicket.id ? realTicket : ticket
            );
          }
        );

        // CRITICAL FIX: Set the real ticket in the detail cache as an array to match useRealtimeQuery format
        queryClient.setQueryData(
          QueryKeys.TICKETS.detail(currentTenantUuid, realTicket.id),
          [realTicket] // Store as array to match useRealtimeQuery expectations
        );

        // CRITICAL FIX: Initialize empty messages cache for the real ticket
        // This prevents unnecessary loading states when transitioning from optimistic ticket
        queryClient.setQueryData(
          QueryKeys.TICKETS.messages(currentTenantUuid, realTicket.id),
          []
        );

        // CRITICAL FIX: Notify about the transition from optimistic to real ticket
        if (onRealTicketCreated) {
          onRealTicketCreated(realTicket.id, optimisticTicket.id);
        }
      }
    },
    onError: (_err, _newTicket, context) => {
      // Rollback on error - restore all previous query data
      if (context?.previousTicketsData) {
        context.previousTicketsData.forEach((data, queryKey) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
    },
    onSettled: (_data, error) => {
      // CRITICAL FIX: Only invalidate on error to prevent unnecessary skeleton loading
      // Successful optimistic ticket creation should remain stable without refetching
      if (error) {
        const currentTenantUuid = tenantUuid || tenantId;
        queryClient.invalidateQueries({
          queryKey: ['tickets', currentTenantUuid, 'list'],
          exact: false,
        });
      }
      // On success, the optimistic ticket is already in place and should remain
    },
  });
};

// Custom mutation hook for updating tickets
export const useUpdateTicket = (tenantId: string) => {
  const queryClient = useQueryClient();

  // CRITICAL FIX: Resolve tenant UUID for proper query key matching
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  return useMutation({
    mutationFn: async ({
      ticketId,
      updates,
    }: {
      ticketId: string;
      updates: UpdateTicketData;
    }) => {
      const response = await fetch(`/api/tickets/${ticketId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...updates,
          tenant_id: tenantId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to update ticket');
      }

      return response.json();
    },
    onMutate: async ({ ticketId, updates }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QueryKeys.TICKETS.detail(tenantId, ticketId),
      });

      // Snapshot previous value
      const previousTicket = queryClient.getQueryData(
        QueryKeys.TICKETS.detail(tenantId, ticketId)
      );

      // Optimistically update cache
      queryClient.setQueryData(
        QueryKeys.TICKETS.detail(tenantId, ticketId),
        (old: Ticket) =>
          old ? { ...old, ...updates, updatedAt: new Date() } : old
      );

      return { previousTicket };
    },
    onError: (_err, { ticketId }, context) => {
      // Rollback on error
      if (context?.previousTicket) {
        queryClient.setQueryData(
          QueryKeys.TICKETS.detail(tenantId, ticketId),
          context.previousTicket
        );
      }
    },
    onSettled: (_data, error, { ticketId }) => {
      // CRITICAL FIX: Only invalidate on error to prevent unnecessary skeleton loading
      // Successful optimistic updates should remain stable without refetching
      if (error) {
        const currentTenantUuid = tenantUuid || tenantId;
        queryClient.invalidateQueries({
          queryKey: QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
        });
        queryClient.invalidateQueries({
          queryKey: ['tickets', currentTenantUuid, 'list'],
          exact: false,
        });
      }
      // On success, the optimistic update is already in place and should remain
    },
  });
};

// Custom hook for adding messages to tickets with optimistic updates
export const useAddTicketMessage = (tenantId: string, ticketId: string) => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // CRITICAL FIX: Resolve tenant UUID for proper query key matching
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  return useMutation({
    mutationFn: async (messageData: {
      content: string;
      attachment_ids?: string[];
      attachments?: Array<{
        id: string;
        name: string;
        type: string;
        size: number;
      }>;
      is_resolve_action?: boolean;
      new_status?: 'pending' | 'resolved' | 'open';
    }) => {
      const response = await fetch(`/api/tickets/${ticketId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...messageData,
          tenant_id: tenantId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to add message');
      }

      return response.json();
    },
    onMutate: async (messageData) => {
      // CRITICAL FIX: Use tenant UUID for proper query key matching
      const currentTenantUuid = tenantUuid || tenantId;

      // Cancel outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QueryKeys.TICKETS.messages(currentTenantUuid, ticketId),
      });

      // Snapshot previous value
      const previousMessages = queryClient.getQueryData(
        QueryKeys.TICKETS.messages(currentTenantUuid, ticketId)
      );

      // Create optimistic message with attachments
      const optimisticAttachments = (messageData.attachments || []).map(
        (att) => ({
          id: att.id,
          name: att.name,
          type: att.type,
          size: att.size,
          url: `/api/attachments/${att.id}`,
          uploadedAt: new Date(),
        })
      );

      const optimisticMessage = {
        id: `optimistic-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        content: messageData.content,
        authorId: user?.id || '',
        authorName:
          `${user?.firstName || ''} ${user?.lastName || ''}`.trim() || 'You',
        authorAvatar: user?.imageUrl || undefined,
        createdAt: new Date(),
        attachments: optimisticAttachments,
        ticketId: ticketId,
      };

      // Optimistically update messages cache
      queryClient.setQueryData(
        QueryKeys.TICKETS.messages(currentTenantUuid, ticketId),
        (old: TicketMessage[] | undefined) => {
          if (!old) return [optimisticMessage];
          return [...old, optimisticMessage];
        }
      );

      // Smart status update if this is a resolve action or has explicit status
      if (messageData.is_resolve_action || messageData.new_status) {
        const newStatus = messageData.new_status || 'resolved';

        // Update ticket detail cache with new status
        queryClient.setQueryData(
          QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
          (old: Ticket | Ticket[] | undefined) => {
            const currentTicket = Array.isArray(old) ? old[0] : old;
            if (!currentTicket) return old;

            const updatedTicket = {
              ...currentTicket,
              status: newStatus as Ticket['status'],
              updatedAt: new Date(),
            };

            return Array.isArray(old) ? [updatedTicket] : updatedTicket;
          }
        );

        // Update ticket list cache
        queryClient.setQueriesData(
          { queryKey: ['tickets', currentTenantUuid, 'list'] },
          (old: Ticket[] | undefined) => {
            if (!Array.isArray(old)) return old;
            return old.map((ticket) =>
              ticket.id === ticketId
                ? {
                    ...ticket,
                    status: newStatus as Ticket['status'],
                    updatedAt: new Date(),
                  }
                : ticket
            );
          }
        );
      }

      return {
        previousMessages,
        expectedStatus: messageData.is_resolve_action
          ? 'resolved'
          : messageData.new_status,
      };
    },
    onSuccess: (_data, variables) => {
      // Show success toast
      const actionText = variables.is_resolve_action ? 'Resolved' : 'Sent';
      toast.success(`Reply ${actionText}`, {
        description: variables.is_resolve_action
          ? 'Ticket has been resolved successfully.'
          : 'Your reply has been sent.',
        duration: 3000,
      });
    },
    onError: (error, _messageData, context) => {
      // Rollback on error
      if (context?.previousMessages) {
        const currentTenantUuid = tenantUuid || tenantId;
        queryClient.setQueryData(
          QueryKeys.TICKETS.messages(currentTenantUuid, ticketId),
          context.previousMessages
        );
      }

      // Show error toast
      toast.error('Failed to Send Reply', {
        description:
          error instanceof Error ? error.message : 'Please try again.',
        duration: 5000,
      });
    },
    onSettled: (_data, error) => {
      // Only invalidate on error to maintain optimistic updates
      if (error) {
        const currentTenantUuid = tenantUuid || tenantId;
        queryClient.invalidateQueries({
          queryKey: QueryKeys.TICKETS.messages(currentTenantUuid, ticketId),
        });
        queryClient.invalidateQueries({
          queryKey: QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
        });
      }
    },
  });
};

// Simple status update mutation with optimistic updates
export const useUpdateTicketStatus = (tenantId: string) => {
  const queryClient = useQueryClient();
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  return useMutation({
    mutationFn: async ({
      ticketId,
      newStatus,
      reason,
    }: {
      ticketId: string;
      newStatus: 'open' | 'pending' | 'resolved' | 'closed';
      reason?: string;
    }) => {
      const response = await fetch(`/api/tickets/${ticketId}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: newStatus,
          reason,
          tenant_id: tenantId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to update status');
      }

      return response.json();
    },
    onMutate: async ({ ticketId, newStatus }) => {
      const currentTenantUuid = tenantUuid || tenantId;

      // Cancel outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
      });

      // Snapshot previous data
      const previousTicket = queryClient.getQueryData(
        QueryKeys.TICKETS.detail(currentTenantUuid, ticketId)
      );

      // Optimistically update ticket detail
      queryClient.setQueryData(
        QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
        (old: Ticket | Ticket[] | undefined) => {
          const currentTicket = Array.isArray(old) ? old[0] : old;
          if (!currentTicket) return old;

          const updatedTicket = {
            ...currentTicket,
            status: newStatus,
            updatedAt: new Date(),
          };

          return Array.isArray(old) ? [updatedTicket] : updatedTicket;
        }
      );

      // Update ticket list cache
      queryClient.setQueriesData(
        { queryKey: ['tickets', currentTenantUuid, 'list'] },
        (old: Ticket[] | undefined) => {
          if (!Array.isArray(old)) return old;
          return old.map((ticket) =>
            ticket.id === ticketId
              ? { ...ticket, status: newStatus, updatedAt: new Date() }
              : ticket
          );
        }
      );

      return { previousTicket };
    },
    onSuccess: (_data, variables) => {
      toast.success('Status Updated', {
        description: `Ticket status changed to ${variables.newStatus}.`,
        duration: 3000,
      });
    },
    onError: (error, variables, context) => {
      // Rollback on error
      if (context?.previousTicket) {
        const currentTenantUuid = tenantUuid || tenantId;
        queryClient.setQueryData(
          QueryKeys.TICKETS.detail(currentTenantUuid, variables.ticketId),
          context.previousTicket
        );
      }

      toast.error('Status Update Failed', {
        description:
          error instanceof Error ? error.message : 'Please try again.',
        duration: 5000,
      });
    },
    onSettled: (_data, error, variables) => {
      // Only invalidate on error
      if (error) {
        const currentTenantUuid = tenantUuid || tenantId;
        queryClient.invalidateQueries({
          queryKey: QueryKeys.TICKETS.detail(
            currentTenantUuid,
            variables.ticketId
          ),
        });
        queryClient.invalidateQueries({
          queryKey: ['tickets', currentTenantUuid, 'list'],
          exact: false,
        });
      }
    },
  });
};
