{"name": "ticketing-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:fast": "next dev --turbopack --experimental-https", "build": "next build", "build:analyze": "ANALYZE=true next build", "build:debug": "next build --debug", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@clerk/nextjs": "^6.27.1", "@hookform/resolvers": "^5.2.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@supabase-cache-helpers/postgrest-react-query": "^1.13.5", "@supabase/supabase-js": "^2.53.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/db-collections": "^0.0.24", "@tanstack/query-sync-storage-persister": "^5.83.0", "@tanstack/react-db": "^0.0.33", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-query-persist-client": "^5.83.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.25", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "idb-keyval": "^6.2.2", "lucide-react": "^0.532.0", "next": "15.4.4", "next-themes": "^0.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.61.1", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "slate": "^0.117.2", "slate-react": "^0.117.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.10", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.32.0", "@next/bundle-analyzer": "^15.4.4", "@next/eslint-plugin-next": "^15.4.4", "@tailwindcss/postcss": "^4", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-window": "^1.8.8", "eslint": "^9.32.0", "eslint-config-next": "15.4.4", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.6.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0"}}