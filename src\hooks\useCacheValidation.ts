/**
 * Enhanced Cache Validation Hook - 2025 Pattern
 *
 * Implements comprehensive silent background validation for all cached data
 * Ensures no stale data persists after browser refresh while maintaining cache-first performance
 */

import { useEffect, useRef, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useTenantStore } from '@/features/tenant/store/use-tenant-store';
import { cleanupStaleTicketQueries } from '@/providers/ReactQueryProvider';

interface CacheValidationOptions {
  // Enable validation for specific data types
  validateTickets?: boolean;
  validateMessages?: boolean;
  validateUsers?: boolean;
  validateSettings?: boolean;
  // Force validation even if queries are disabled
  forceValidation?: boolean;
}

/**
 * Comprehensive cache validation hook that ensures all cached data is validated
 * against the server on browser refresh or when explicitly triggered
 */
export const useCacheValidation = (options: CacheValidationOptions = {}) => {
  const queryClient = useQueryClient();
  const { user, isLoaded } = useAuth();
  const tenantId = useTenantStore((state) => state.tenantId);
  const hasValidatedRef = useRef(false);

  const {
    validateTickets = true,
    validateMessages = true,
    validateUsers = true,
    validateSettings = true,
    forceValidation = false,
  } = options;

  // Comprehensive validation function
  const validateAllCachedData = useCallback(async () => {
    if (!isLoaded || !user || !tenantId) {
      return;
    }

    console.log('🔄 Starting comprehensive cache validation...');

    try {
      // Get all cached queries for this tenant
      const allQueries = queryClient.getQueriesData({
        queryKey: [undefined], // Get all queries
      });

      // Track validation promises
      const validationPromises: Promise<unknown>[] = [];

      // Validate ticket-related data
      if (validateTickets) {
        // Find all ticket list queries
        const ticketListQueries = allQueries.filter(
          ([queryKey]) =>
            Array.isArray(queryKey) &&
            queryKey[0] === 'tickets' &&
            queryKey[1] === tenantId &&
            queryKey[2] === 'list'
        );

        ticketListQueries.forEach(([queryKey]) => {
          console.log('🎫 Validating ticket list:', queryKey);
          validationPromises.push(
            queryClient.refetchQueries({
              queryKey: queryKey as string[],
              type: 'active',
            })
          );
        });

        // Find all individual ticket queries
        const ticketDetailQueries = allQueries.filter(
          ([queryKey]) =>
            Array.isArray(queryKey) &&
            queryKey[0] === 'tickets' &&
            queryKey[1] === tenantId &&
            queryKey[2] === 'detail'
        );

        ticketDetailQueries.forEach(([queryKey]) => {
          console.log('🎫 Validating ticket detail:', queryKey);
          const ticketId = (queryKey as string[])[3]; // Extract ticket ID from query key
          validationPromises.push(
            queryClient
              .refetchQueries({
                queryKey: queryKey as string[],
                type: 'active',
              })
              .catch((error) => {
                // Handle 404s gracefully - remove invalid tickets from cache
                if (error.message.includes('404')) {
                  console.warn(
                    '🗑️ Removing non-existent ticket from cache:',
                    ticketId
                  );
                  queryClient.removeQueries({
                    queryKey: queryKey as string[],
                  });
                }
                // Don't re-throw - continue with other validations
              })
          );
        });
      }

      // Validate message data
      if (validateMessages) {
        const messageQueries = allQueries.filter(
          ([queryKey]) =>
            Array.isArray(queryKey) &&
            queryKey[0] === 'tickets' &&
            queryKey[1] === tenantId &&
            queryKey[3] === 'messages'
        );

        messageQueries.forEach(([queryKey]) => {
          console.log('💬 Validating messages:', queryKey);
          const ticketId = (queryKey as string[])[3]; // Extract ticket ID from query key
          validationPromises.push(
            queryClient
              .refetchQueries({
                queryKey: queryKey as string[],
                type: 'active',
              })
              .catch((error) => {
                // Handle 404s gracefully - remove invalid message queries from cache
                if (error.message.includes('404')) {
                  console.warn(
                    '🗑️ Removing messages for non-existent ticket:',
                    ticketId
                  );
                  queryClient.removeQueries({
                    queryKey: queryKey as string[],
                  });
                }
                // Don't re-throw - continue with other validations
              })
          );
        });
      }

      // Validate user data
      if (validateUsers) {
        const userQueries = allQueries.filter(
          ([queryKey]) =>
            Array.isArray(queryKey) &&
            (queryKey[0] === 'users' || queryKey[0] === 'user')
        );

        userQueries.forEach(([queryKey]) => {
          console.log('👤 Validating user data:', queryKey);
          validationPromises.push(
            queryClient.refetchQueries({
              queryKey: queryKey as string[],
              type: 'active',
            })
          );
        });
      }

      // Validate settings
      if (validateSettings) {
        const settingsQueries = allQueries.filter(
          ([queryKey]) => Array.isArray(queryKey) && queryKey[0] === 'settings'
        );

        settingsQueries.forEach(([queryKey]) => {
          console.log('⚙️ Validating settings:', queryKey);
          validationPromises.push(
            queryClient.refetchQueries({
              queryKey: queryKey as string[],
              type: 'active',
            })
          );
        });
      }

      // Execute all validations in parallel
      if (validationPromises.length > 0) {
        await Promise.allSettled(validationPromises);
        console.log(
          `✅ Cache validation completed (${validationPromises.length} queries validated)`
        );

        // CRITICAL FIX: Clean up any stale ticket queries after validation
        cleanupStaleTicketQueries(queryClient, tenantId);
      } else {
        console.log('ℹ️ No cached data found to validate');
      }
    } catch (error) {
      console.error('❌ Cache validation failed:', error);
      // Don't throw - validation errors shouldn't break the app
    }
  }, [
    queryClient,
    isLoaded,
    user,
    tenantId,
    validateTickets,
    validateMessages,
    validateUsers,
    validateSettings,
  ]);

  // Automatic validation on mount (browser refresh)
  useEffect(() => {
    if (!hasValidatedRef.current && isLoaded && user && tenantId) {
      hasValidatedRef.current = true;
      // Use setTimeout to avoid blocking initial render
      const timeoutId = setTimeout(() => {
        validateAllCachedData();
      }, 100);

      return () => clearTimeout(timeoutId);
    }
    return undefined;
  }, [isLoaded, user, tenantId, validateAllCachedData]);

  // Force validation when explicitly requested
  useEffect(() => {
    if (forceValidation) {
      validateAllCachedData();
    }
  }, [forceValidation, validateAllCachedData]);

  return {
    validateAllCachedData,
    isValidating: queryClient.isFetching() > 0,
  };
};

/**
 * Hook specifically for validating ticket-related data
 * Useful for components that only need ticket data validation
 */
export const useTicketCacheValidation = () => {
  return useCacheValidation({
    validateTickets: true,
    validateMessages: true,
    validateUsers: false,
    validateSettings: false,
  });
};

/**
 * Hook for validating all data on critical operations
 * Use after login, tenant switching, or other state changes
 */
export const useComprehensiveCacheValidation = () => {
  return useCacheValidation({
    validateTickets: true,
    validateMessages: true,
    validateUsers: true,
    validateSettings: true,
  });
};
