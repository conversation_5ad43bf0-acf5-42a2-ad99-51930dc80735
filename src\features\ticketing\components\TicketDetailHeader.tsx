'use client';

import { Badge } from '@/features/shared/components/ui/badge';
import { Button } from '@/features/shared/components/ui/button';
import { RefreshCcw, TicketX } from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import {
  TicketPriority,
  Department,
  TicketWithDetails,
  AssignedUser,
} from '../models/ticket.schema';
import { useUserDatabaseId } from '@/features/shared/hooks/useUserDatabaseId';
import { useMemo } from 'react';

// Utility function to format user roles for display
const formatUserRole = (role: string): string => {
  switch (role.toLowerCase()) {
    case 'super_admin':
      return 'Super Admin';
    case 'admin':
      return 'Admin';
    case 'agent':
      return 'Agent';
    case 'user':
      return 'User';
    default:
      return role.charAt(0).toUpperCase() + role.slice(1);
  }
};

// Type for ticket metadata that may contain assignedUser
interface TicketMetadata extends Record<string, unknown> {
  assignedUser?: AssignedUser;
  assignment?: {
    assignedByUser?: {
      id: string;
      name: string;
      email: string;
      role: string;
    };
    assigned_at?: string;
    auto_assigned?: boolean;
  };
}
import { useAuth } from '@/features/shared/hooks/useAuth';
import { InteractivePriorityBadge } from './InteractivePriorityBadge';
import { InteractiveDepartmentBadge } from './InteractiveDepartmentBadge';
import { statusColors, formatTicketStatus } from '../config/ticket-options';

interface TicketDetailHeaderProps {
  ticket: TicketWithDetails;
  currentStatus?: string | undefined;
  currentPriority?: TicketPriority | undefined;
  currentDepartment?: Department | undefined;
  isOpeningTicket: boolean;
  handleOpenTicketInDetail: () => void;
  handleReopenTicket: () => void;
  handleCloseTicket: () => void;
  handlePriorityChange: (newPriority: TicketPriority) => void;
  handleDepartmentChange: (newDepartment: Department) => void;
}

export function TicketDetailHeader({
  ticket,
  currentStatus,
  currentPriority,
  currentDepartment,
  isOpeningTicket,
  handleOpenTicketInDetail,
  handleReopenTicket,
  handleCloseTicket,
  handlePriorityChange,
  handleDepartmentChange,
}: TicketDetailHeaderProps) {
  const { role, user } = useAuth();
  const { userDatabaseId } = useUserDatabaseId();

  // Use current values if provided, otherwise fall back to ticket values
  const displayStatus = currentStatus || ticket.status;
  const displayPriority = currentPriority || ticket.priority;
  const displayDepartment = currentDepartment || ticket.department;

  // Get assignedUser from ticket metadata or direct property
  const assignedUser =
    ticket.assignedUser || (ticket.metadata as TicketMetadata)?.assignedUser;

  // Check if current user is the assignee
  const isAssignedToCurrentUser = useMemo(() => {
    if (!ticket) return false;
    // Check both database UUID and Clerk ID for assignment
    return (
      ticket.assignedTo === userDatabaseId ||
      ticket.assignedToClerkId === user?.id
    );
  }, [ticket, userDatabaseId, user?.id]);

  // Get assignment information for display
  const assignmentInfo = useMemo(() => {
    if (!ticket.assignedTo || !assignedUser) return null;

    const assignmentMeta = (ticket.metadata as TicketMetadata)?.assignment;

    if (isAssignedToCurrentUser) {
      // Current user is the assignee - show who assigned it to them
      let assignedByUser = assignmentMeta?.assignedByUser;

      // If assignedByUser is not available, construct it from assignment metadata
      // This handles cases where the assignment metadata doesn't include full user info
      if (!assignedByUser && assignmentMeta) {
        // Get role from assignment metadata (assigned_by_role)
        const assignerRole = String(
          (assignmentMeta as Record<string, unknown>)?.assigned_by_role ||
            'user'
        );

        // Try to get name from createdByUser metadata first
        if (ticket.metadata?.createdByUser) {
          assignedByUser = {
            id: ticket.metadata.createdByUser.id,
            name: ticket.metadata.createdByUser.name,
            email: ticket.metadata.createdByUser.email,
            role: assignerRole, // Use the role from assignment metadata
          };
        }
        // Fallback to ticket properties (userName, userEmail, etc.)
        else if (ticket.userName) {
          assignedByUser = {
            id: ticket.userId || 'unknown',
            name: ticket.userName,
            email: ticket.userEmail,
            role: assignerRole, // Use the role from assignment metadata
          };
        }
      }

      if (assignedByUser) {
        return {
          label: 'Assigned by:',
          user: assignedByUser,
          date: assignmentMeta?.assigned_at || ticket.assignedAt,
        };
      }
    }

    // Current user is not the assignee OR no assignment info available - show who it's assigned to
    return {
      label: 'Assigned to:',
      user: assignedUser,
      date: assignmentMeta?.assigned_at || ticket.assignedAt,
    };
  }, [ticket, assignedUser, isAssignedToCurrentUser]);

  return (
    <div className='p-6 border-b border-gray-200 dark:border-gray-700 shrink-0'>
      {/* Top row: Status, Priority, Department badges and Ticket actions */}
      <div className='flex items-center justify-between mb-3'>
        <div className='flex flex-wrap gap-2'>
          {displayStatus && (
            <Badge
              className={cn(
                'text-xs transition-all duration-300 ease-in-out',
                statusColors[displayStatus]
              )}
            >
              {formatTicketStatus(displayStatus)}
            </Badge>
          )}
          {ticket.metadata?.assignment?.auto_assigned &&
            (role === 'agent' ||
              role === 'admin' ||
              role === 'super_admin') && (
              <Badge
                variant='secondary'
                className='text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 transition-all duration-300 ease-in-out'
              >
                Auto Assigned
              </Badge>
            )}
          <InteractivePriorityBadge
            currentPriority={displayPriority}
            onPriorityChange={handlePriorityChange}
          />
          <InteractiveDepartmentBadge
            currentDepartment={displayDepartment}
            onDepartmentChange={handleDepartmentChange}
          />
        </div>
        {/* Action buttons for agents, admins, and super_admins */}
        {role !== 'user' && (
          <div className='flex gap-2 transition-all duration-300 ease-in-out'>
            {displayStatus === 'resolved' && (
              <>
                <Button
                  onClick={handleReopenTicket}
                  disabled={isOpeningTicket}
                  variant='outline'
                  className='transition-all duration-200 ease-in-out'
                >
                  <RefreshCcw className='h-4 w-4 mr-2 transition-transform duration-200' />
                  {isOpeningTicket ? 'Reopening...' : 'Reopen'}
                </Button>
                <Button
                  onClick={handleCloseTicket}
                  variant='outline'
                  className='transition-all duration-200 ease-in-out'
                >
                  <TicketX className='h-4 w-4 mr-2' />
                  Close this ticket
                </Button>
              </>
            )}
            {displayStatus === 'closed' && (
              <Button
                onClick={handleReopenTicket}
                disabled={isOpeningTicket}
                variant='outline'
                className='transition-all duration-200 ease-in-out'
              >
                <RefreshCcw className='h-4 w-4 mr-2 transition-transform duration-200' />
                {isOpeningTicket ? 'Reopening...' : 'Reopen this ticket'}
              </Button>
            )}
            {displayStatus === 'new' && (
              <Button
                onClick={handleOpenTicketInDetail}
                disabled={isOpeningTicket}
                className='bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-600 dark:hover:bg-blue-700 disabled:opacity-50 transition-all duration-200 ease-in-out'
              >
                <RefreshCcw className='h-4 w-4 mr-2 transition-transform duration-200' />
                {isOpeningTicket ? 'Opening...' : 'Open This Ticket'}
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Ticket title - Remove bottom margin for users */}
      <h1
        className={`text-xl font-semibold text-gray-900 dark:text-gray-100 ${role !== 'user' ? 'mb-3' : ''}`}
      >
        {ticket.title}
      </h1>

      {/* Assignment information row - below title - Show for all roles when assigned */}
      {(role !== 'user' || assignmentInfo) && (
        <div className='flex items-center justify-between'>
          {assignmentInfo ? (
            <div className='flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400'>
              <div className='flex items-center gap-2'>
                <span className='font-medium'>{assignmentInfo.label}</span>
                <span>{assignmentInfo.user.name}</span>
                <Badge variant='outline' className='text-xs'>
                  {formatUserRole(assignmentInfo.user.role)}
                </Badge>
                {/* Add assignment date */}
                {assignmentInfo.date && (
                  <span className='text-xs text-gray-500'>
                    on {format(new Date(assignmentInfo.date), 'MMM d, yyyy')}
                  </span>
                )}
              </div>
            </div>
          ) : (
            <div className='text-sm text-gray-600 dark:text-gray-400'>
              <span className='font-medium'>Assign To (Admins & Agents)</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
