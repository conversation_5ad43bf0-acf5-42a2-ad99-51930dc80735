{"permissions": {"allow": ["<PERSON><PERSON>(powershell:*)", "<PERSON><PERSON>(claude mcp get:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_go_back", "mcp__playwright__browser_go_forward", "mcp__playwright__browser_tab_list", "mcp__playwright__browser_tab_new", "mcp__playwright__browser_tab_select", "mcp__playwright__browser_tab_close", "mcp__playwright__browser_click", "mcp__playwright__browser_type", "mcp__playwright__browser_hover", "mcp__playwright__browser_press_key", "mcp__playwright__browser_select_option", "mcp__playwright__browser_file_upload", "mcp__playwright__browser_handle_dialog", "mcp__playwright__browser_drag", "mcp__playwright__browser_resize", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_evaluate", "mcp__playwright__browser_network_requests", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_close", "mcp__playwright__browser_install", "mcp__playwright__playwright_navigate", "mcp__playwright__playwright_screenshot", "mcp__playwright__playwright_get_visible_text", "mcp__playwright__playwright_get_visible_html", "mcp__playwright__playwright_console_logs", "mcp__playwright__playwright_evaluate", "mcp__playwright__playwright_fill", "mcp__playwright__playwright_click", "Bash(npm install:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(npm run lint)", "Bash(npm run type-check:*)", "Bash(find:*)", "Bash(npm run typecheck:*)", "mcp__taskmanager__request_planning", "mcp__taskmanager__add_tasks_to_request", "mcp__taskmanager__get_next_task", "mcp__taskmanager__mark_task_done", "mcp__taskmanager__approve_task_completion", "mcp__taskmanager__approve_request_completion", "mcp__taskmanager__list_requests", "mcp__taskmanager__open_task_details", "mcp__taskmanager__update_task", "mcp__taskmanager__delete_task", "WebFetch(domain:clerk.com)", "mcp__sequential-thinking__sequentialthinking", "Bash(npm run lint:*)", "<PERSON><PERSON>(npx prettier:*)", "<PERSON><PERSON>(sed:*)", "Bash(npx tsc:*)", "Bash(export:*)", "mcp__playwright__browser_wait_for", "<PERSON><PERSON>(tasklist:*)", "<PERSON><PERSON>(del analyze-db.js)", "Bash(npx supabase:*)", "Bash(winget install:*)", "Bash(npx:*)", "Bash(set SUPABASE_ACCESS_TOKEN=********************************************)", "WebFetch(domain:xprwqadnmauhpschgkwk.supabase.co)", "<PERSON><PERSON>(curl:*)", "mcp__supabase__get_project_url", "mcp__supabase__get_anon_key", "mcp__supabase__list_tables", "mcp__supabase__list_migrations", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(grep:*)", "mcp__playwright__browser_navigate_forward", "mcp__playwright__browser_navigate_back"], "deny": []}}