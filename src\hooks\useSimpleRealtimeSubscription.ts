/**
 * Simple Real-time Subscription Hook - 2025 Best Practices
 *
 * Following TanStack Query best practices:
 * - Use invalidation instead of manual cache updates
 * - Let React Query handle refetching and caching
 * - Minimal code for maximum reliability
 */

import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useSupabaseClient } from '@/lib/supabase-clerk';
import { QueryKeys } from '@/lib/query-keys';

export function useSimpleRealtimeSubscription(
  tenantId: string,
  enabled = true
) {
  const { supabase } = useSupabaseClient();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!enabled || !tenantId || !supabase) return;

    console.log(
      `🔄 Setting up simple real-time subscription for tenant: ${tenantId}`
    );

    // Single channel for all real-time events
    const channel = supabase
      .channel(`realtime-${tenantId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tickets',
          filter: `tenant_id=eq.${tenantId}`,
        },
        (payload) => {
          console.log('🎫 Ticket change detected:', payload.eventType);

          // Simple invalidation - let React Query handle the rest
          queryClient.invalidateQueries({
            queryKey: ['tickets', tenantId],
            refetchType: 'active', // Only refetch if component is mounted
          });
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'ticket_messages',
          filter: `tenant_id=eq.${tenantId}`,
        },
        (payload) => {
          const payloadNew = payload.new as Record<string, unknown>;
          const payloadOld = payload.old as Record<string, unknown>;
          const ticketId = payloadNew?.ticket_id || payloadOld?.ticket_id;

          if (ticketId) {
            console.log('💬 Message change detected for ticket:', ticketId);

            // Invalidate specific ticket messages
            queryClient.invalidateQueries({
              queryKey: QueryKeys.TICKETS.messages(
                tenantId,
                ticketId as string
              ),
              refetchType: 'active',
            });

            // Also invalidate ticket detail to update last message info
            queryClient.invalidateQueries({
              queryKey: QueryKeys.TICKETS.detail(tenantId, ticketId as string),
              refetchType: 'active',
            });
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'users',
          filter: `tenant_id=eq.${tenantId}`,
        },
        () => {
          console.log('👤 User change detected');

          // Invalidate user queries
          queryClient.invalidateQueries({
            queryKey: QueryKeys.USERS.all(tenantId),
            refetchType: 'active',
          });
        }
      )
      .subscribe((status) => {
        console.log(`📡 Real-time subscription status: ${status}`);
      });

    return () => {
      console.log(
        `🔌 Cleaning up real-time subscription for tenant: ${tenantId}`
      );
      supabase.removeChannel(channel);
    };
  }, [enabled, tenantId, supabase, queryClient]);
}
