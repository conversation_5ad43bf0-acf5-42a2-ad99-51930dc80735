/**
 * Unified Real-time Subscription Manager - 2025 Centralized Pattern
 *
 * Single source of truth for all real-time events in the ticketing application.
 * Implements singleton pattern to ensure only one active subscription per tenant.
 *
 * Key Features:
 * - Single channel per tenant for all real-time events
 * - Smart cache updates for all related React Query caches
 * - Proper connection lifecycle management
 * - Zero complexity with minimal code approach
 *
 * <AUTHOR> Augster
 * @version 1.0 - Centralized Subscription Manager (January 2025)
 */

import { useEffect, useMemo, useRef } from 'react';
import { useQueryClient, QueryClient } from '@tanstack/react-query';
import type {
  RealtimePostgresChangesPayload,
  RealtimeChannel,
  SupabaseClient,
} from '@supabase/supabase-js';
import { useSupabaseClient } from '@/lib/supabase-clerk';
import type { Database } from '@/types/supabase';
import RealtimeDataService from '@/lib/services/realtime-data.service';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useUserDatabaseId } from '@/features/shared/hooks/useUserDatabaseId';
import { useTenantUuid } from '@/hooks/useRealtimeQuery';
import { QueryKeys } from '@/lib/query-keys';

// Type definitions for database rows
type TicketRow = Database['public']['Tables']['tickets']['Row'];
type MessageRow = Database['public']['Tables']['ticket_messages']['Row'];
type UserRow = Database['public']['Tables']['users']['Row'];

// Type for items with id property
interface ItemWithId {
  id: string;
  [key: string]: unknown;
}

// Type for ticket data with common fields
interface TicketData extends ItemWithId {
  status?: string;
  priority?: string;
  department?: string;
  assignedTo?: string;
  updatedAt?: string | Date;
  title?: string;
  description?: string;
}

// Type for message data with common fields
interface MessageData extends ItemWithId {
  content?: string;
  updatedAt?: string | Date;
  isEdited?: boolean;
  attachments?: unknown[];
}

// Global connection registry to prevent any duplicate connections
const globalConnectionRegistry = new Map<string, boolean>();

// 2025 Enhanced Singleton Subscription Manager with Connection Optimization
class UnifiedSubscriptionManager {
  private static instances = new Map<string, UnifiedSubscriptionManager>();
  private channel: RealtimeChannel | null = null;
  private subscribers = new Set<string>();
  private tenantUuid: string;
  private supabase: SupabaseClient<Database>;
  private queryClient: QueryClient;
  private realtimeDataService: RealtimeDataService;
  private userDatabaseId: string | null;
  private isCreatingSubscription = false;
  private tenantSubdomain: string | null = null;

  // 2025 Connection Optimization Features
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private connectionMonitor: NodeJS.Timeout | null = null;
  private fallbackPolling: NodeJS.Timeout | null = null;
  private lastHeartbeat: number = Date.now();
  private connectionRetryCount = 0;
  private maxRetries = 5;
  private isConnectionHealthy = true;
  private fallbackActive = false;

  private constructor(
    tenantUuid: string,
    supabase: SupabaseClient<Database>,
    queryClient: QueryClient,
    realtimeDataService: RealtimeDataService,
    userDatabaseId: string | null
  ) {
    this.tenantUuid = tenantUuid;
    this.supabase = supabase;
    this.queryClient = queryClient;
    this.realtimeDataService = realtimeDataService;
    this.userDatabaseId = userDatabaseId;

    // Initialize tenant subdomain lookup
    this.initializeTenantSubdomain();
  }

  // Get tenant subdomain from UUID for proper cache key matching
  private async initializeTenantSubdomain(): Promise<void> {
    try {
      const { data, error } = await this.supabase
        .from('tenants')
        .select('subdomain')
        .eq('id', this.tenantUuid)
        .single();

      if (!error && data) {
        this.tenantSubdomain = data.subdomain;
        console.log(
          `🔍 Resolved tenant subdomain: ${this.tenantSubdomain} for UUID: ${this.tenantUuid}`
        );
      }
    } catch (error) {
      console.warn('Failed to resolve tenant subdomain:', error);
    }
  }

  static getInstance(
    tenantUuid: string,
    supabase: SupabaseClient<Database>,
    queryClient: QueryClient,
    realtimeDataService: RealtimeDataService,
    userDatabaseId: string | null
  ): UnifiedSubscriptionManager {
    // ROBUST SINGLETON: Only create new instance if none exists
    if (this.instances.has(tenantUuid)) {
      const existingInstance = this.instances.get(tenantUuid)!;
      console.log(
        `♻️ Reusing existing subscription for tenant ${tenantUuid} @ ${window.location.href}`
      );
      return existingInstance;
    }

    console.log(
      `🔗 Creating new subscription for tenant ${tenantUuid} @ ${window.location.href}`
    );

    const instance = new UnifiedSubscriptionManager(
      tenantUuid,
      supabase,
      queryClient,
      realtimeDataService,
      userDatabaseId
    );

    this.instances.set(tenantUuid, instance);
    return instance;
  }

  addSubscriber(subscriberId: string): void {
    this.subscribers.add(subscriberId);

    console.log(
      `🔗 Added subscriber ${subscriberId} to tenant ${this.tenantUuid}. Total subscribers: ${this.subscribers.size}`
    );

    if (
      this.subscribers.size === 1 &&
      !this.channel &&
      !this.isCreatingSubscription
    ) {
      console.log(
        `🚀 First subscriber for tenant ${this.tenantUuid} - creating subscription`
      );
      this.createSubscription();
    } else if (this.channel) {
      console.log(
        `♻️ Reusing existing subscription for tenant ${this.tenantUuid}`
      );
    } else if (this.isCreatingSubscription) {
      console.log(
        `⏳ Subscription already being created for tenant ${this.tenantUuid} - waiting`
      );
    }
  }

  removeSubscriber(subscriberId: string): void {
    this.subscribers.delete(subscriberId);

    console.log(
      `🔌 Removed subscriber ${subscriberId} from tenant ${this.tenantUuid}. Remaining subscribers: ${this.subscribers.size}`
    );

    if (this.subscribers.size === 0 && this.channel) {
      console.log(
        `🛑 No more subscribers for tenant ${this.tenantUuid} - destroying subscription`
      );
      this.destroySubscription();
    }
  }

  private createSubscription(): void {
    if (
      this.isCreatingSubscription ||
      this.channel ||
      globalConnectionRegistry.get(this.tenantUuid)
    ) {
      console.log(
        `🚫 Subscription already exists or being created for tenant ${this.tenantUuid}`
      );
      return;
    }

    this.isCreatingSubscription = true;
    globalConnectionRegistry.set(this.tenantUuid, true);

    const channelName = `unified-realtime-${this.tenantUuid}`;
    console.log(
      `🔄 Creating unified real-time subscription for tenant: ${this.tenantUuid}`
    );
    console.log(`📡 Channel name: ${channelName}`);
    console.log(`👥 Subscriber count: ${this.subscribers.size}`);

    this.channel = this.supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tickets',
          filter: `tenant_id=eq.${this.tenantUuid}`,
        },
        (payload: RealtimePostgresChangesPayload<TicketRow>) =>
          this.handleTicketEvent(payload)
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'ticket_messages',
          filter: `tenant_id=eq.${this.tenantUuid}`,
        },
        (payload: RealtimePostgresChangesPayload<MessageRow>) =>
          this.handleMessageEvent(payload)
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'users',
          filter: `tenant_id=eq.${this.tenantUuid}`,
        },
        (payload: RealtimePostgresChangesPayload<UserRow>) =>
          this.handleUserEvent(payload)
      )
      .on('system', {}, (payload) => this.handleSystemEvent(payload))
      .subscribe((status) => this.handleSubscriptionStatus(status));

    this.isCreatingSubscription = false;
    console.log(
      `✅ Subscription created successfully for tenant ${this.tenantUuid}`
    );

    // 2025 Optimization: Start connection health monitoring
    this.startConnectionMonitoring();
    this.startSimpleFallback();
  }

  private destroySubscription(): void {
    if (this.channel) {
      console.log(
        '🔄 Destroying unified real-time subscription for tenant:',
        this.tenantUuid
      );
      this.supabase.removeChannel(this.channel);
      this.channel = null;
    }

    // 2025 Optimization: Clean up monitoring intervals
    this.stopConnectionMonitoring();
    this.stopSimpleFallback();

    this.isCreatingSubscription = false;
    globalConnectionRegistry.delete(this.tenantUuid);
  }

  private async handleTicketEvent(
    payload: RealtimePostgresChangesPayload<TicketRow>
  ): Promise<void> {
    try {
      const { eventType, new: newRow, old: oldRow } = payload;

      // CRITICAL FIX: Skip ticket UPDATE events that are caused by current user's message replies
      // This prevents cache duplication issues when users send replies
      if (
        this.userDatabaseId &&
        eventType === 'UPDATE' &&
        newRow &&
        oldRow &&
        'updated_at' in newRow &&
        'updated_at' in oldRow
      ) {
        // Check if this is a minor update (only updated_at changed) which typically happens after message replies
        const oldUpdatedAt = new Date(oldRow.updated_at || 0).getTime();
        const newUpdatedAt = new Date(newRow.updated_at || 0).getTime();
        const timeDiff = newUpdatedAt - oldUpdatedAt;

        // If the update happened within 5 seconds and only updated_at changed, likely a message reply update
        if (timeDiff < 5000) {
          // Check if any significant fields changed (not just timestamp)
          const significantFields = [
            'status',
            'priority',
            'assigned_to',
            'title',
            'description',
          ];
          const hasSignificantChanges = significantFields.some(
            (field) =>
              oldRow[field as keyof TicketRow] !==
              newRow[field as keyof TicketRow]
          );

          if (!hasSignificantChanges) {
            console.log(
              `⏭️ Skipping minor ticket UPDATE event for current user's reply: ${newRow.id}`
            );
            return;
          }
        }
      }

      console.log(
        '🎫 Ticket event received:',
        eventType,
        newRow && typeof newRow === 'object' && 'id' in newRow
          ? newRow.id
          : 'unknown'
      );

      if (eventType === 'DELETE' && oldRow && 'id' in oldRow) {
        // Handle ticket deletion with proper React Query patterns
        await this.handleTicketDeletion(oldRow.id);
        return;
      }

      if (!newRow || !('id' in newRow)) return;

      // Transform ticket data using RealtimeDataService
      const transformedTicket =
        await this.realtimeDataService.transformTicketRow(newRow as TicketRow);

      // Use React Query optimistic updates for real-time synchronization
      await this.optimisticTicketUpdate(
        newRow.id,
        transformedTicket,
        eventType
      );
    } catch (error) {
      console.error('Error handling ticket event:', error);
    }
  }

  // 2025 React Query Best Practice: Proper ticket deletion handling
  private async handleTicketDeletion(ticketId: string): Promise<void> {
    console.log(`🗑️ Handling ticket deletion: ${ticketId}`);

    // CRITICAL FIX: Use specific pattern to only update ticket LIST caches, not message caches
    this.queryClient.setQueriesData(
      { queryKey: ['tickets', this.tenantUuid, 'list'] },
      (oldData: unknown) => {
        if (!Array.isArray(oldData)) return oldData;
        return oldData.filter((ticket: ItemWithId) => ticket.id !== ticketId);
      }
    );

    // SKELETON FIX: Remove invalidation that causes unnecessary skeleton loading
    // The cache updates above already handle the data changes
    // No need to invalidate and refetch - the data is already updated
  }

  // 2025 React Query Best Practice: Optimistic updates for real-time events
  private async optimisticTicketUpdate(
    ticketId: string,
    transformedTicket: unknown,
    eventType: string
  ): Promise<void> {
    console.log(
      `🎫 CRITICAL: Optimistic update for ticket: ${ticketId} (${eventType}) - ensuring all users see status changes`
    );

    // CRITICAL FIX: Update caches using BOTH tenantUuid AND tenantSubdomain
    // This ensures status changes are visible to ALL users, including the sender
    const tenantSubdomain = this.tenantSubdomain;

    // CRITICAL FIX: Use specific pattern to only update ticket LIST caches, not message caches
    // This prevents phantom messages by avoiding corruption of message arrays with ticket update logic
    this.queryClient.setQueriesData(
      { queryKey: ['tickets', this.tenantUuid, 'list'] },
      (oldData: unknown) => {
        if (!Array.isArray(oldData)) return oldData;
        return this.updateTicketInArray(
          oldData,
          ticketId,
          transformedTicket,
          eventType
        );
      }
    );

    // CRITICAL: Also update subdomain-based caches (what components actually use)
    // CRITICAL FIX: Use specific pattern to only update ticket LIST caches, not message caches
    if (tenantSubdomain && tenantSubdomain !== this.tenantUuid) {
      this.queryClient.setQueriesData(
        { queryKey: ['tickets', tenantSubdomain, 'list'] },
        (oldData: unknown) => {
          if (!Array.isArray(oldData)) return oldData;
          return this.updateTicketInArray(
            oldData,
            ticketId,
            transformedTicket,
            eventType
          );
        }
      );

      // Update specific ticket detail cache (subdomain-based) - THIS IS THE CRITICAL FIX
      console.log(
        `🔄 CRITICAL: Updating detail cache with subdomain key: ${tenantSubdomain}`
      );
      this.queryClient.setQueryData(
        QueryKeys.TICKETS.detail(tenantSubdomain, ticketId),
        transformedTicket
      );
      console.log(
        `✅ CRITICAL: Detail cache updated with subdomain key: ${tenantSubdomain}`
      );
    }

    // Update specific ticket detail cache (UUID-based)
    this.queryClient.setQueryData(
      QueryKeys.TICKETS.detail(this.tenantUuid, ticketId),
      transformedTicket
    );

    // PERFORMANCE OPTIMIZATION: Smart background pre-fetch for ticket details
    // Pre-fetch ticket messages and related data to eliminate loading delays
    await this.smartPreFetchTicketDetails(ticketId, eventType);

    // SKELETON FIX: Remove background sync that causes skeleton loading
    // The direct cache updates above already ensure data consistency
    // No need for additional refetches that trigger skeleton states
  }

  // Helper method to update ticket in array with smart field-level updates
  private updateTicketInArray(
    oldData: unknown[],
    ticketId: string,
    transformedTicket: unknown,
    eventType: string
  ): unknown[] {
    switch (eventType) {
      case 'INSERT': {
        // CRITICAL FIX: Check for existing ticket (optimistic or real) before inserting
        // This prevents duplicate keys when real-time events arrive after optimistic updates
        const existingIndex = oldData.findIndex((ticket: unknown) => {
          const typedTicket = ticket as ItemWithId;
          return typedTicket.id === ticketId;
        });

        if (existingIndex !== -1) {
          // Ticket already exists (likely optimistic), smart merge with real data
          const newData = [...oldData];
          const existingTicket = newData[existingIndex] as TicketData;
          newData[existingIndex] = this.smartMergeTicket(
            existingTicket,
            transformedTicket
          );
          console.log(
            `🔄 Smart merged existing ticket at index ${existingIndex} for ID: ${ticketId}`
          );
          return newData;
        } else {
          // New ticket, add to beginning
          console.log(`➕ Added new ticket to beginning for ID: ${ticketId}`);
          return [transformedTicket, ...oldData];
        }
      }
      case 'UPDATE':
        return oldData.map((ticket: unknown) => {
          const typedTicket = ticket as ItemWithId;
          if (typedTicket.id === ticketId) {
            // Smart merge - only update changed fields
            return this.smartMergeTicket(ticket, transformedTicket);
          }
          return ticket;
        });
      default:
        return oldData;
    }
  }

  // 2025 Smart Cache: Only update fields that actually changed
  private smartMergeTicket(existing: unknown, incoming: unknown): unknown {
    const existingTicket = existing as TicketData;
    const incomingTicket = incoming as TicketData;
    const merged = { ...existingTicket };
    const changedFields: string[] = [];

    // Check key fields that commonly change
    const fieldsToCheck = [
      'status',
      'priority',
      'department',
      'assignedTo',
      'updatedAt',
      'title',
      'description',
    ];

    fieldsToCheck.forEach((field) => {
      const oldValue = existingTicket[field];
      const newValue = incomingTicket[field];

      // Simple equality check (handles most cases)
      if (oldValue !== newValue) {
        merged[field] = newValue;
        changedFields.push(field);
      }
    });

    if (changedFields.length > 0) {
      console.log(
        `🔄 Smart merge updated fields: ${changedFields.join(', ')} for ticket ${incomingTicket.id}`
      );
    } else {
      console.log(
        `✨ No field changes detected for ticket ${incomingTicket.id}`
      );
    }

    return merged;
  }

  // SKELETON FIX: Removed backgroundSyncTicketData function that caused skeleton loading
  // Direct cache updates are sufficient and don't trigger skeleton states

  // PERFORMANCE OPTIMIZATION: Smart pre-fetch ticket details when real-time updates occur
  private async smartPreFetchTicketDetails(
    ticketId: string,
    eventType: string
  ): Promise<void> {
    try {
      // Pre-fetch for both UPDATE and INSERT events to ensure complete data
      console.log(
        `🚀 Smart pre-fetching ticket details for: ${ticketId} (${eventType})`
      );

      // Pre-fetch full ticket details if not already cached or incomplete
      const detailKey = QueryKeys.TICKETS.detail(
        this.tenantSubdomain || this.tenantUuid,
        ticketId
      );
      const existingDetailData = this.queryClient.getQueryData(detailKey);

      // Check if we have complete ticket data (including user fields)
      const needsDetailFetch =
        !existingDetailData ||
        (existingDetailData &&
          (() => {
            const data = Array.isArray(existingDetailData)
              ? existingDetailData[0]
              : existingDetailData;
            // Check if critical user fields are missing
            return !data || !data.userName || !data.userEmail;
          })());

      if (needsDetailFetch) {
        // Pre-fetch complete ticket details with user data
        this.queryClient.prefetchQuery({
          queryKey: detailKey,
          queryFn: async () => {
            try {
              const response = await fetch(
                `/api/tickets/${ticketId}?tenant_id=${this.tenantUuid}`
              );
              if (!response.ok) return null;
              const result = await response.json();
              return result.data || result;
            } catch {
              return null;
            }
          },
          staleTime: 0, // Always fetch fresh data
        });
        console.log(`📋 Pre-fetching complete ticket details for: ${ticketId}`);
      }

      // Pre-fetch ticket messages in the background
      const messagesKey = QueryKeys.TICKETS.messages(
        this.tenantSubdomain || this.tenantUuid,
        ticketId
      );

      // Check if messages are already cached
      const existingMessages = this.queryClient.getQueryData(messagesKey);
      if (!existingMessages || !Array.isArray(existingMessages)) {
        // Pre-fetch messages silently in background
        this.queryClient.prefetchQuery({
          queryKey: messagesKey,
          queryFn: async () => {
            try {
              const response = await fetch(
                `/api/tickets/${ticketId}/messages?tenant_id=${this.tenantUuid}`
              );
              if (!response.ok) return [];
              const data = await response.json();
              return data.messages || [];
            } catch {
              return [];
            }
          },
          staleTime: 0, // Always fetch fresh data
        });

        console.log(`📬 Pre-fetching messages for ticket: ${ticketId}`);
      }

      // For subdomain-based cache, also pre-fetch
      if (this.tenantSubdomain && this.tenantSubdomain !== this.tenantUuid) {
        const subdomainMessagesKey = QueryKeys.TICKETS.messages(
          this.tenantSubdomain,
          ticketId
        );

        const existingSubdomainMessages =
          this.queryClient.getQueryData(subdomainMessagesKey);
        if (
          !existingSubdomainMessages ||
          !Array.isArray(existingSubdomainMessages)
        ) {
          this.queryClient.prefetchQuery({
            queryKey: subdomainMessagesKey,
            queryFn: async () => {
              try {
                const response = await fetch(
                  `/api/tickets/${ticketId}/messages?tenant_id=${this.tenantUuid}`
                );
                if (!response.ok) return [];
                const data = await response.json();
                return data.messages || [];
              } catch {
                return [];
              }
            },
            staleTime: 0,
          });
        }
      }

      console.log(`✅ Pre-fetch initiated for ticket: ${ticketId}`);
    } catch (error) {
      console.warn(`⚠️ Pre-fetch failed for ticket ${ticketId}:`, error);
      // Don't throw - pre-fetching is a performance optimization, not critical
    }
  }

  // 2025 React Query Best Practice: Optimistic message updates for real-time events
  private async optimisticMessageUpdate(
    ticketId: string,
    payload: RealtimePostgresChangesPayload<MessageRow>,
    eventType: string
  ): Promise<void> {
    try {
      const { new: newRow, old: oldRow } = payload;

      console.log(
        `💬 Optimistic message update for ticket: ${ticketId} (${eventType})`
      );

      if (eventType === 'INSERT' && newRow) {
        // CRITICAL FIX: Add tenant_id to the message payload if it's missing
        // Real-time payloads might not include tenant_id, but we know it from the subscription filter
        const messageWithTenant = {
          ...newRow,
          tenant_id: this.tenantUuid,
        } as MessageRow & { tenant_id: string };

        // Transform message data using RealtimeDataService
        const transformedMessage =
          await this.realtimeDataService.transformMessageRow(
            messageWithTenant as MessageRow
          );

        // CRITICAL FIX: Only update UUID-based cache to prevent message duplication
        // UI components use UUID-based cache keys, so updating both UUID and subdomain caches
        // causes the same message to appear twice in the UI
        this.updateMessageCaches(ticketId, transformedMessage, 'INSERT');

        console.log(
          `💬 Message added to UUID-based cache for ticket: ${ticketId}`
        );
      } else if (eventType === 'UPDATE' && newRow) {
        // CRITICAL FIX: Add tenant_id to the message payload if it's missing
        const messageWithTenant = {
          ...newRow,
          tenant_id: this.tenantUuid,
        } as MessageRow & { tenant_id: string };

        // Transform updated message data
        const transformedMessage =
          await this.realtimeDataService.transformMessageRow(
            messageWithTenant as MessageRow
          );

        // CRITICAL FIX: Only update UUID-based cache to prevent message duplication
        this.updateMessageCaches(ticketId, transformedMessage, 'UPDATE');

        console.log(
          `💬 Message updated in UUID-based cache for ticket: ${ticketId}`
        );
      } else if (eventType === 'DELETE' && oldRow) {
        // CRITICAL FIX: Only update UUID-based cache to prevent message duplication
        this.updateMessageCaches(ticketId, oldRow, 'DELETE');

        console.log(
          `💬 Message deleted from UUID-based cache for ticket: ${ticketId}`
        );
      }

      console.log(
        `✅ Optimistic message update completed for ticket: ${ticketId}`
      );
    } catch (error) {
      console.error(
        `❌ Optimistic message update failed for ticket ${ticketId}:`,
        error
      );
    }
  }

  // Helper method to update message caches with smart incremental updates
  private updateMessageCaches(
    ticketId: string,
    messageData: unknown,
    eventType: string
  ): void {
    const messageQueryKey = QueryKeys.TICKETS.messages(
      this.tenantUuid,
      ticketId
    );

    switch (eventType) {
      case 'INSERT':
        this.queryClient.setQueryData(
          messageQueryKey,
          (oldData: unknown[] | undefined) => {
            if (!Array.isArray(oldData)) return [messageData];

            // CRITICAL FIX: Enhanced duplicate detection for optimistic updates
            const incomingMessage = messageData as MessageData;
            const messageId = incomingMessage?.id;
            const messageContent = incomingMessage?.content;
            const messageAuthorId = incomingMessage?.authorId;
            const messageCreatedAt = incomingMessage?.createdAt;

            // Check for exact ID match (normal case)
            const existsById = oldData.some(
              (msg: unknown) => (msg as MessageData).id === messageId
            );

            if (existsById) {
              console.log(
                `✨ Message ${messageId} already exists by ID, skipping duplicate`
              );
              return oldData;
            }

            // CRITICAL FIX: Enhanced duplicate detection for optimistic vs real-time messages
            // This handles the case where optimistic message has "optimistic-*" ID and real message has database ID
            const existsByContentAndAuthor = oldData.some((msg: unknown) => {
              const existingMsg = msg as MessageData;

              // Same content (exact match) - this is the most reliable indicator
              if (existingMsg.content !== messageContent) {
                return false;
              }

              // CRITICAL FIX: For duplicate detection, prioritize content and timing over strict authorId matching
              // AuthorId can differ between optimistic (Clerk ID) and real-time (database ID) for the same user
              const existingAuthorId = existingMsg.authorId;
              const sameAuthorById = existingAuthorId === messageAuthorId;

              // Check if timestamps are within 10 seconds (increased tolerance for race conditions)
              const existingTime = new Date(
                existingMsg.createdAt as string | number | Date
              ).getTime();
              const incomingTime = new Date(
                messageCreatedAt as string | number | Date
              ).getTime();
              const timeDiff = Math.abs(existingTime - incomingTime);

              // For messages with same content and close timing, consider them duplicates
              // even if authorId differs (handles optimistic vs real-time ID mismatch)
              if (timeDiff < 10000) {
                // If it's the same author by ID, definitely a duplicate
                if (sameAuthorById) {
                  console.log(
                    `🚫 DUPLICATE DETECTED: Content="${messageContent?.substring(0, 50)}..." AuthorId="${messageAuthorId}" TimeDiff=${timeDiff}ms`
                  );
                  return true;
                }

                // CRITICAL FIX: For messages with same content and close timing but different authorId,
                // check if existing message is optimistic (likely the user's own message)
                const isExistingOptimistic =
                  existingMsg.id &&
                  typeof existingMsg.id === 'string' &&
                  existingMsg.id.startsWith('optimistic-');

                if (isExistingOptimistic) {
                  console.log(
                    `🚫 DUPLICATE DETECTED (Optimistic): Content="${messageContent?.substring(0, 50)}..." TimeDiff=${timeDiff}ms - replacing optimistic with real message`
                  );
                  return true;
                }
              }

              return false;
            });

            if (existsByContentAndAuthor) {
              console.log(
                `✨ Message already exists by content/author/time, replacing optimistic with real message`
              );
              // Replace the optimistic message with the real one
              return oldData.map((msg: unknown) => {
                const existingMsg = msg as MessageData;

                // CRITICAL FIX: Enhanced replacement logic to match the enhanced duplicate detection
                // Replace if content matches and either authorId matches OR it's an optimistic message with close timing
                const contentMatches = existingMsg.content === messageContent;
                const authorIdMatches =
                  existingMsg.authorId === messageAuthorId;
                const isOptimistic =
                  existingMsg.id &&
                  typeof existingMsg.id === 'string' &&
                  existingMsg.id.startsWith('optimistic-');

                // Check timing for optimistic messages
                const existingTime = new Date(
                  existingMsg.createdAt as string | number | Date
                ).getTime();
                const incomingTime = new Date(
                  messageCreatedAt as string | number | Date
                ).getTime();
                const timeDiff = Math.abs(existingTime - incomingTime);
                const closeInTime = timeDiff < 10000;

                // Replace if: same content + (same authorId OR optimistic message with close timing)
                if (
                  contentMatches &&
                  (authorIdMatches || (isOptimistic && closeInTime))
                ) {
                  console.log(
                    `🔄 Replacing ${isOptimistic ? 'optimistic' : 'existing'} message ${existingMsg.id} with real message ${(messageData as MessageData).id} (AuthorMatch: ${authorIdMatches}, TimeDiff: ${timeDiff}ms)`
                  );
                  return messageData; // Replace optimistic/duplicate with real message
                }
                return msg;
              });
            }

            console.log(
              `💬 Adding new message ${messageId} to ticket ${ticketId}`
            );
            return [...oldData, messageData];
          }
        );
        break;

      case 'UPDATE':
        this.queryClient.setQueryData(
          messageQueryKey,
          (oldData: unknown[] | undefined) => {
            if (!Array.isArray(oldData)) return [messageData];
            return oldData.map((msg: unknown) => {
              const typedMsg = msg as { id: string };
              const typedMessageData = messageData as { id: string };
              if (typedMsg.id === typedMessageData.id) {
                // Smart merge for messages too
                return this.smartMergeMessage(msg, messageData);
              }
              return msg;
            });
          }
        );
        break;

      case 'DELETE':
        this.queryClient.setQueryData(
          messageQueryKey,
          (oldData: unknown[] | undefined) => {
            if (!Array.isArray(oldData)) return [];
            const typedMessageData = messageData as { id: string };
            return oldData.filter((msg: unknown) => {
              const typedMsg = msg as { id: string };
              return typedMsg.id !== typedMessageData.id;
            });
          }
        );
        break;
    }
  }

  // 2025 Smart Cache: Smart message merging
  private smartMergeMessage(existing: unknown, incoming: unknown): unknown {
    const existingMsg = existing as MessageData;
    const incomingMsg = incoming as MessageData;
    const merged = { ...existingMsg };
    const changedFields: string[] = [];

    // Check key message fields that commonly change
    const fieldsToCheck = ['content', 'updatedAt', 'isEdited', 'attachments'];

    fieldsToCheck.forEach((field) => {
      const oldValue = existingMsg[field];
      const newValue = incomingMsg[field];

      if (oldValue !== newValue) {
        merged[field] = newValue;
        changedFields.push(field);
      }
    });

    if (changedFields.length > 0) {
      console.log(
        `💬 Smart merge updated message fields: ${changedFields.join(', ')} for message ${incomingMsg.id}`
      );
    }

    return merged;
  }

  private async handleMessageEvent(
    payload: RealtimePostgresChangesPayload<MessageRow>
  ): Promise<void> {
    try {
      const { eventType, new: newRow, old: oldRow } = payload;

      // CRITICAL FIX: Removed problematic current user skip logic
      // Enhanced duplicate detection in updateMessageCaches now handles optimistic vs real message conflicts
      // Skipping current user's messages entirely was causing issues with optimistic updates

      console.log(
        '💬 Message event received:',
        eventType,
        newRow && 'id' in newRow ? newRow.id : 'unknown'
      );

      const ticketId =
        (newRow && 'ticket_id' in newRow ? newRow.ticket_id : null) ||
        (oldRow && 'ticket_id' in oldRow ? oldRow.ticket_id : null);
      if (!ticketId) return;

      // Use React Query optimistic updates for real-time message synchronization
      await this.optimisticMessageUpdate(ticketId, payload, eventType);

      // PERFORMANCE OPTIMIZATION: Pre-fetch ticket details when messages are added
      // This ensures ticket details are ready when users click on tickets with new messages
      if (eventType === 'INSERT' || eventType === 'UPDATE') {
        await this.smartPreFetchTicketDetails(ticketId, 'UPDATE');
      }

      // SKELETON FIX: Remove background sync to prevent skeleton loading
      // Message updates already handle all necessary cache updates
    } catch (error) {
      console.error('Error handling message event:', error);
    }
  }

  private async handleUserEvent(
    payload: RealtimePostgresChangesPayload<UserRow>
  ): Promise<void> {
    try {
      const { eventType, new: newRow } = payload;

      console.log(
        '👤 User event received:',
        eventType,
        newRow && 'id' in newRow ? newRow.id : 'unknown'
      );

      if (!newRow || !('id' in newRow)) return;

      // User changes can affect ticket assignments and display
      await this.queryClient.invalidateQueries({
        queryKey: QueryKeys.USERS.all(this.tenantUuid),
      });

      // CRITICAL FIX: Invalidate only ticket LIST caches, not message caches
      // This prevents unnecessary invalidation of message queries when user data changes
      await this.queryClient.invalidateQueries({
        queryKey: ['tickets', this.tenantUuid, 'list'],
      });

      // Legacy cache updates for backward compatibility
      this.updateUserCaches(/* newRow.id */);
      this.invalidateTicketCachesForUser(/* newRow.id */);
    } catch (error) {
      console.error('Error handling user event:', error);
    }
  }

  private updateUserCaches(/* userId: string */): void {
    // Invalidate user caches
    // Note: userId parameter reserved for future specific user cache invalidation
    this.queryClient.invalidateQueries({
      queryKey: QueryKeys.USERS.all(this.tenantUuid),
    });
  }

  private invalidateTicketCachesForUser(/* userId: string */): void {
    // CRITICAL FIX: Invalidate only ticket LIST caches, not message caches
    // This prevents unnecessary invalidation of message queries when user data changes
    // Note: userId parameter reserved for future specific user-related ticket cache invalidation
    this.queryClient.invalidateQueries({
      queryKey: ['tickets', this.tenantUuid, 'list'],
    });
  }

  // 2025 Connection Optimization: Enhanced heartbeat mechanism for 30+ minute idle periods
  private startConnectionMonitoring(): void {
    // More aggressive heartbeat every 15 seconds to keep connection alive during long idle periods
    this.heartbeatInterval = setInterval(() => {
      if (this.channel && this.isConnectionHealthy) {
        this.channel.send({
          type: 'broadcast',
          event: 'heartbeat',
          payload: { timestamp: Date.now() },
        });
        this.lastHeartbeat = Date.now();
        console.log(`💓 Heartbeat sent for tenant ${this.tenantUuid}`);
      }
    }, 15000); // 15 seconds for better idle period handling

    // More frequent connection health monitor every 30 seconds
    this.connectionMonitor = setInterval(() => {
      this.checkConnectionHealth();
    }, 30000); // 30 seconds for faster issue detection

    console.log(
      `🔍 Enhanced connection monitoring started for tenant ${this.tenantUuid}`
    );
  }

  // 2025 Connection Optimization: Stop monitoring when subscription is destroyed
  private stopConnectionMonitoring(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.connectionMonitor) {
      clearInterval(this.connectionMonitor);
      this.connectionMonitor = null;
    }

    console.log(
      `🛑 Connection monitoring stopped for tenant ${this.tenantUuid}`
    );
  }

  // 2025 Enhanced Fallback: More aggressive fallback activation
  private startSimpleFallback(): void {
    this.fallbackPolling = setInterval(() => {
      if (!this.isConnectionHealthy && !this.fallbackActive) {
        console.log(
          `🔄 Starting enhanced fallback with TanStack Query coordination for tenant ${this.tenantUuid}`
        );
        this.fallbackActive = true;
        this.performEnhancedFallbackSync();
      }
    }, 15 * 1000); // 15 seconds for faster fallback activation
  }

  private stopSimpleFallback(): void {
    if (this.fallbackPolling) {
      clearInterval(this.fallbackPolling);
      this.fallbackPolling = null;
    }
    this.fallbackActive = false;
  }

  // Enhanced fallback sync with TanStack Query coordination
  private async performEnhancedFallbackSync(): Promise<void> {
    if (this.isConnectionHealthy) {
      this.fallbackActive = false;
      return;
    }

    try {
      console.log(
        `📡 Enhanced fallback sync with TanStack Query for tenant ${this.tenantUuid}`
      );

      // Additional validation for critical ticket status data
      await this.validateCriticalTicketStatuses();
    } catch (error) {
      console.error('Enhanced fallback sync failed:', error);
    }
  }

  // 2025 Enhanced: Validate critical ticket statuses during fallback
  private async validateCriticalTicketStatuses(): Promise<void> {
    try {
      console.log(
        `🎯 Validating critical ticket statuses for tenant ${this.tenantUuid}`
      );

      // Get all ticket detail queries that might have pending status changes
      const allQueries = this.queryClient.getQueryCache().getAll();
      const ticketDetailQueries = allQueries.filter((query) => {
        const key = query.queryKey;
        return (
          Array.isArray(key) &&
          key.includes('tickets') &&
          key.includes('detail') &&
          (key.includes(this.tenantUuid) ||
            (this.tenantSubdomain && key.includes(this.tenantSubdomain)))
        );
      });

      // Validate each ticket's status with server
      for (const query of ticketDetailQueries) {
        const ticketData = query.state.data;
        if (ticketData) {
          const ticket = Array.isArray(ticketData) ? ticketData[0] : ticketData;
          if (ticket && ticket.id) {
            await this.validateSingleTicketStatus(ticket.id, ticket.status);
          }
        }
      }
    } catch (error) {
      console.error('Critical status validation failed:', error);
    }
  }

  // Validate individual ticket status with smart retry
  private async validateSingleTicketStatus(
    ticketId: string,
    cachedStatus: string
  ): Promise<void> {
    try {
      // Use API endpoint to get proper user data transformation
      const response = await fetch(
        `/api/tickets/${ticketId}?tenant_id=${this.tenantUuid}`
      );

      if (!response.ok) {
        if (response.status === 404) {
          console.warn(
            `🗑️ Ticket ${ticketId} no longer exists, removing from cache`
          );
          // Remove non-existent ticket from cache
          this.queryClient?.removeQueries({
            queryKey: ['tickets', this.tenantUuid, 'detail', ticketId],
          });
          this.queryClient?.removeQueries({
            queryKey: ['tickets', this.tenantUuid, 'list'],
            exact: false,
          });
          return;
        }
        console.warn(
          `⚠️ Could not validate status for ticket ${ticketId}: API call failed`
        );
        return;
      }

      const apiResponse = await response.json();
      if (!apiResponse.success || !apiResponse.data) {
        console.warn(
          `⚠️ Could not validate status for ticket ${ticketId}: Invalid API response`
        );
        return;
      }

      const serverTicket = apiResponse.data;

      // Check for status mismatch including user data
      const hasChanges =
        serverTicket.status !== cachedStatus ||
        // Also check if this is part of a broader data sync (user data, etc.)
        serverTicket.userName !== undefined; // If we have user data, update regardless

      if (hasChanges) {
        console.log(
          `🔄 Status/data mismatch detected for ticket ${ticketId}: updating with complete server data`
        );

        // Update cache with complete server data (including user data)
        const detailKey = QueryKeys.TICKETS.detail(this.tenantUuid, ticketId);
        this.queryClient.setQueryData(detailKey, (oldData: unknown) => {
          if (Array.isArray(oldData) && oldData[0]) {
            return [
              {
                ...oldData[0],
                ...serverTicket, // Merge complete server data
              },
            ];
          } else if (oldData) {
            return {
              ...oldData,
              ...serverTicket, // Merge complete server data
            };
          }
          return oldData;
        });

        // Also update ticket list cache with complete data
        this.queryClient.setQueriesData(
          { queryKey: ['tickets', this.tenantUuid, 'list'] },
          (old: unknown[] | undefined) => {
            if (!Array.isArray(old)) return old;
            return old.map((ticket: unknown) => {
              const typedTicket = ticket as {
                id: string;
                [key: string]: unknown;
              };
              return typedTicket.id === ticketId
                ? {
                    ...typedTicket,
                    ...serverTicket, // Merge complete server data
                  }
                : ticket;
            });
          }
        );
      }
    } catch (error) {
      console.warn(
        `⚠️ Status validation failed for ticket ${ticketId}:`,
        error
      );
    }
  }

  // 2025 Enhanced Connection Health: More aggressive monitoring for 30+ minute idle periods
  private checkConnectionHealth(): void {
    const now = Date.now();
    const timeSinceLastHeartbeat = now - this.lastHeartbeat;
    const healthThreshold = 45000; // 45 seconds - much more aggressive for idle periods

    if (timeSinceLastHeartbeat > healthThreshold) {
      console.warn(
        `⚠️ Real-time connection degraded for tenant ${this.tenantUuid} (${timeSinceLastHeartbeat}ms since last heartbeat), activating fallback`
      );
      this.isConnectionHealthy = false;
      this.attemptConnectionRecovery();
    } else {
      this.isConnectionHealthy = true;
      this.connectionRetryCount = 0; // Reset retry count on healthy connection
      if (this.fallbackActive) {
        console.log(
          `✅ Real-time connection recovered, coordinating with TanStack Query for tenant ${this.tenantUuid}`
        );
        this.fallbackActive = false;

        // Simple cache validation after recovery
        console.log('Real-time connection recovered, cache is in sync');
      }
    }
  }

  // 2025 Connection Optimization: Smart reconnection with exponential backoff
  private attemptConnectionRecovery(): void {
    if (this.connectionRetryCount >= this.maxRetries) {
      console.error(
        `❌ Max reconnection attempts reached for tenant ${this.tenantUuid}`
      );
      return;
    }

    this.connectionRetryCount++;
    const backoffDelay = Math.min(
      1000 * Math.pow(2, this.connectionRetryCount),
      30000
    );

    console.log(
      `🔄 Attempting connection recovery for tenant ${this.tenantUuid} (attempt ${this.connectionRetryCount}/${this.maxRetries})`
    );

    setTimeout(() => {
      try {
        if (this.channel) {
          this.supabase.removeChannel(this.channel);
          this.channel = null;
        }

        // Reset flags and recreate subscription
        this.isCreatingSubscription = false;
        globalConnectionRegistry.delete(this.tenantUuid);
        this.createSubscription();
      } catch (error) {
        console.error(`❌ Error during connection recovery:`, error);
        // Reset retry count and try again after a longer delay
        this.connectionRetryCount = Math.max(0, this.connectionRetryCount - 1);
        setTimeout(() => this.attemptConnectionRecovery(), 5000);
      }
    }, backoffDelay);
  }

  // 2025 Connection Optimization: Handle system events for connection monitoring
  private handleSystemEvent(payload: {
    type?: string;
    [key: string]: unknown;
  }): void {
    console.log(`🔧 System event for tenant ${this.tenantUuid}:`, payload);

    if (payload.type === 'pong') {
      this.lastHeartbeat = Date.now();
      this.isConnectionHealthy = true;
      console.log(`💓 Pong received for tenant ${this.tenantUuid}`);
    }
  }

  // 2025 Connection Optimization: Handle subscription status changes
  private handleSubscriptionStatus(status: string): void {
    console.log(
      `📡 Subscription status for tenant ${this.tenantUuid}: ${status}`
    );

    switch (status) {
      case 'SUBSCRIBED':
        this.isConnectionHealthy = true;
        this.connectionRetryCount = 0;
        this.lastHeartbeat = Date.now();
        if (this.fallbackActive) {
          console.log(
            `✅ Real-time reconnected, stopping fallback for tenant ${this.tenantUuid}`
          );
          this.fallbackActive = false;
        }
        break;
      case 'CHANNEL_ERROR':
      case 'TIMED_OUT':
        this.isConnectionHealthy = false;
        this.attemptConnectionRecovery();
        break;
      case 'CLOSED':
        this.isConnectionHealthy = false;
        break;
    }
  }

  // Instance cleanup method for immediate cleanup
  cleanup(): void {
    console.log(`🧹 CLEANUP: Starting cleanup for tenant ${this.tenantUuid}`);

    // Clear all subscribers
    this.subscribers.clear();

    // Destroy subscription immediately
    this.destroySubscription();

    console.log(`✅ CLEANUP: Cleanup completed for tenant ${this.tenantUuid}`);
  }

  static cleanup(tenantUuid: string): void {
    const instance = this.instances.get(tenantUuid);
    if (instance && instance.subscribers.size === 0) {
      instance.destroySubscription();
      this.instances.delete(tenantUuid);
    }
  }

  // Enhanced connection status check with fallback trigger
  getConnectionStatus(): {
    isConnected: boolean;
    fallbackActive: boolean;
    lastHeartbeat: number;
  } {
    return {
      isConnected: this.isConnectionHealthy,
      fallbackActive: this.fallbackActive,
      lastHeartbeat: this.lastHeartbeat,
    };
  }

  // Force validation check (for click-based fallback)
  async forceValidation(): Promise<void> {
    if (!this.isConnectionHealthy || this.fallbackActive) {
      console.log(
        `🔍 Force validation triggered for tenant ${this.tenantUuid}`
      );
      await this.performEnhancedFallbackSync();
    }
  }

  static getConnectionStatus(tenantUuid: string): {
    isConnected: boolean;
    fallbackActive: boolean;
    lastHeartbeat: number;
  } | null {
    const instance = this.instances.get(tenantUuid);
    return instance ? instance.getConnectionStatus() : null;
  }
}

/**
 * Simple hook to get real-time connection status
 */
export function useRealtimeConnectionStatus(tenantId: string): {
  isConnected: boolean;
  fallbackActive: boolean;
  lastHeartbeat: number;
} | null {
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  if (!tenantUuid) return null;

  return UnifiedSubscriptionManager.getConnectionStatus(tenantUuid);
}

/**
 * PERFORMANCE OPTIMIZED: Smart background cache validation - only triggers when connection is unhealthy
 */
export function useSmartCacheRefresh(tenantId: string): void {
  const { supabase } = useSupabaseClient();
  const queryClient = useQueryClient();
  const { isLoggingOut } = useAuth();
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  useEffect(() => {
    if (!tenantUuid || !supabase || isLoggingOut) return;

    // PERFORMANCE FIX: Only perform background validation if real-time connection is unhealthy
    const performSmartRefresh = async () => {
      try {
        // Check if real-time connection is healthy
        const connectionStatus =
          UnifiedSubscriptionManager.getConnectionStatus(tenantUuid);
        const isConnectionHealthy = connectionStatus?.isConnected !== false;

        if (isConnectionHealthy) {
          console.log(
            '✅ Real-time connection is healthy, skipping background validation to avoid duplicate API calls'
          );
          return;
        }

        console.log(
          '🔄 Real-time connection unhealthy, starting smart background cache validation'
        );

        // Get all cached queries for this tenant
        const allQueries = queryClient.getQueryCache().getAll();

        // Find all ticket detail and message queries that might be stale
        const ticketQueries = allQueries.filter((query) => {
          const key = query.queryKey;
          return (
            Array.isArray(key) &&
            key.includes('tickets') &&
            (key.includes(tenantUuid) || key.includes(tenantId))
          );
        });

        console.log(
          `📊 Found ${ticketQueries.length} cached ticket queries to validate`
        );

        // Only validate if connection is actually unhealthy
        if (!isConnectionHealthy) {
          // Validate each cached ticket query in background
          for (const query of ticketQueries) {
            const key = query.queryKey;

            // Handle ticket detail queries
            if (key.includes('detail')) {
              const ticketId = key[key.length - 1] as string;
              if (ticketId && typeof ticketId === 'string') {
                await validateSingleTicketInBackground(
                  ticketId,
                  supabase,
                  queryClient,
                  tenantUuid
                );
              }
            }

            // Handle ticket list queries - validate recent tickets
            else if (key.includes('list')) {
              await validateTicketListInBackground(
                supabase,
                queryClient,
                tenantUuid
              );
            }
          }
        }

        console.log('✅ Smart background cache validation completed');
      } catch (error) {
        console.warn('⚠️ Smart cache validation failed:', error);
      }
    };

    // Delay to let initial queries settle, then validate in background
    const timeout = setTimeout(performSmartRefresh, 2000);

    return () => clearTimeout(timeout);
  }, [tenantUuid, supabase, queryClient, isLoggingOut, tenantId]);
}

// Background validation helper for individual tickets
async function validateSingleTicketInBackground(
  ticketId: string,
  supabase: SupabaseClient<Database>,
  queryClient: QueryClient,
  tenantUuid: string
): Promise<void> {
  try {
    // Get cached ticket
    const cachedTicket = queryClient.getQueryData(
      QueryKeys.TICKETS.detail(tenantUuid, ticketId)
    );

    if (!cachedTicket) return;

    // Fetch server data via API endpoint (includes user data transformation)
    const response = await fetch(
      `/api/tickets/${ticketId}?tenant_id=${tenantUuid}`
    );

    if (!response.ok) {
      if (response.status === 404) {
        console.warn(
          `🗑️ Background validation: ticket ${ticketId} no longer exists, removing from cache`
        );
        // Remove non-existent ticket from cache
        queryClient.removeQueries({
          queryKey: ['tickets', tenantUuid, 'detail', ticketId],
        });
        queryClient.removeQueries({
          queryKey: ['tickets', tenantUuid, 'list'],
          exact: false,
        });
      }
      return;
    }

    const apiResponse = await response.json();
    if (!apiResponse.success || !apiResponse.data) return;

    const serverTicket = apiResponse.data;

    // Check for changes and update if needed
    const cached = Array.isArray(cachedTicket) ? cachedTicket[0] : cachedTicket;
    if (cached) {
      const hasChanges =
        cached.status !== serverTicket.status ||
        cached.priority !== serverTicket.priority ||
        cached.department !== serverTicket.department ||
        cached.assignedTo !== serverTicket.assignedTo ||
        cached.userName !== serverTicket.userName ||
        cached.userEmail !== serverTicket.userEmail ||
        cached.userAvatar !== serverTicket.userAvatar ||
        new Date(cached.updatedAt).getTime() !==
          new Date(serverTicket.updatedAt || 0).getTime();

      if (hasChanges) {
        console.log(
          `🔄 Background validation: updating stale data for ticket ${ticketId}`
        );

        // Update detail cache
        queryClient.setQueryData(
          QueryKeys.TICKETS.detail(tenantUuid, ticketId),
          (oldData: unknown) => {
            if (Array.isArray(oldData)) {
              return [{ ...oldData[0], ...serverTicket }];
            }
            return { ...(oldData as object), ...serverTicket };
          }
        );

        // Update list cache
        queryClient.setQueriesData(
          { queryKey: ['tickets', tenantUuid, 'list'] },
          (oldData: unknown) => {
            if (!Array.isArray(oldData)) return oldData;
            return oldData.map((ticket: unknown) => {
              const typedTicket = ticket as {
                id: string;
                [key: string]: unknown;
              };
              return typedTicket.id === ticketId
                ? { ...typedTicket, ...serverTicket }
                : ticket;
            });
          }
        );

        // Also check for new messages
        await validateTicketMessagesInBackground(
          ticketId,
          supabase,
          queryClient,
          tenantUuid
        );
      }
    }
  } catch (error) {
    console.warn(
      `⚠️ Background validation failed for ticket ${ticketId}:`,
      error
    );
  }
}

// Background validation helper for ticket messages
async function validateTicketMessagesInBackground(
  ticketId: string,
  supabase: SupabaseClient<Database>,
  queryClient: QueryClient,
  tenantUuid: string
): Promise<void> {
  try {
    const cachedMessages = queryClient.getQueryData(
      QueryKeys.TICKETS.messages(tenantUuid, ticketId)
    ) as unknown[] | undefined;

    const { data: serverMessages, error } = await supabase
      .from('ticket_messages')
      .select('*')
      .eq('ticket_id', ticketId)
      .eq('tenant_id', tenantUuid)
      .order('created_at', { ascending: true });

    if (error || !serverMessages) return;

    const cachedCount = cachedMessages?.length || 0;
    const serverCount = serverMessages.length;

    if (serverCount > cachedCount) {
      console.log(
        `💬 Background validation: found ${serverCount - cachedCount} new messages for ticket ${ticketId}`
      );

      queryClient.setQueryData(
        QueryKeys.TICKETS.messages(tenantUuid, ticketId),
        serverMessages
      );
    }
  } catch (error) {
    console.warn(
      `⚠️ Background message validation failed for ticket ${ticketId}:`,
      error
    );
  }
}

// Background validation helper for ticket lists
async function validateTicketListInBackground(
  _supabase: SupabaseClient<Database>,
  queryClient: QueryClient,
  tenantUuid: string
): Promise<void> {
  try {
    // Get list of cached tickets to validate
    const cachedListQueries = queryClient
      .getQueryCache()
      .getAll()
      .filter((query) => {
        const key = query.queryKey;
        return (
          Array.isArray(key) &&
          key.includes('tickets') &&
          key.includes('list') &&
          key.includes(tenantUuid)
        );
      });

    if (cachedListQueries.length === 0) return;

    // Get cached ticket IDs for validation
    const cachedTickets: unknown[] = [];
    cachedListQueries.forEach((query) => {
      const data = query.state.data;
      if (Array.isArray(data)) {
        cachedTickets.push(...data);
      }
    });

    if (cachedTickets.length === 0) return;

    console.log(
      `🔍 Background validation: checking ${cachedTickets.length} cached tickets`
    );

    // Validate each cached ticket using API endpoint for proper user data transformation
    let hasUpdates = false;
    const validatedTickets = new Map<string, unknown>();

    for (const cachedTicket of cachedTickets.slice(0, 10)) {
      // Limit to 10 most recent
      const typedTicket = cachedTicket as {
        id: string;
        [key: string]: unknown;
      };
      const ticketId = typedTicket.id;

      try {
        // Use API endpoint to get proper user data transformation
        const response = await fetch(
          `/api/tickets/${ticketId}?tenant_id=${tenantUuid}`
        );

        if (!response.ok) continue;

        const apiResponse = await response.json();
        if (!apiResponse.success || !apiResponse.data) continue;

        const serverTicket = apiResponse.data;

        // Compare with cached data including user fields
        const hasChanges =
          typedTicket.status !== serverTicket.status ||
          typedTicket.priority !== serverTicket.priority ||
          typedTicket.department !== serverTicket.department ||
          typedTicket.assignedTo !== serverTicket.assignedTo ||
          typedTicket.userName !== serverTicket.userName ||
          typedTicket.userEmail !== serverTicket.userEmail ||
          typedTicket.userAvatar !== serverTicket.userAvatar ||
          new Date(typedTicket.updatedAt as string | Date).getTime() !==
            new Date(serverTicket.updatedAt || 0).getTime();

        if (hasChanges) {
          console.log(
            `🔄 Background validation: detected changes for ticket ${ticketId}`
          );
          hasUpdates = true;
          validatedTickets.set(ticketId, serverTicket);
        }
      } catch (error) {
        console.warn(
          `⚠️ Background validation failed for ticket ${ticketId}:`,
          error
        );
      }
    }

    // Update list cache with validated data
    if (hasUpdates) {
      queryClient.setQueriesData(
        { queryKey: ['tickets', tenantUuid, 'list'] },
        (oldData: unknown) => {
          if (!Array.isArray(oldData)) return oldData;

          const updatedData = oldData.map((cachedTicket: unknown) => {
            const typedCachedTicket = cachedTicket as {
              id: string;
              [key: string]: unknown;
            };
            const validatedTicket = validatedTickets.get(typedCachedTicket.id);
            return validatedTicket
              ? { ...typedCachedTicket, ...validatedTicket }
              : cachedTicket;
          });

          console.log(
            `🔄 Background validation: updated ticket list with ${validatedTickets.size} changes`
          );

          return updatedData;
        }
      );
    }
  } catch (error) {
    console.warn('⚠️ Background ticket list validation failed:', error);
  }
}

/**
 * Unified Real-time Subscription Hook
 *
 * Provides centralized real-time subscription management for all components.
 * Implements singleton pattern to ensure only one connection per tenant.
 */
export function useUnifiedRealtimeSubscription(tenantId: string): void {
  const { supabase } = useSupabaseClient();
  const queryClient = useQueryClient();
  const { user, isLoggingOut } = useAuth();
  const { userDatabaseId } = useUserDatabaseId();

  // Resolve tenant UUID
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  // Initialize RealtimeDataService with QueryClient for cache-first user lookups
  // CRITICAL FIX: Pass queryClient to enable cache-first user data retrieval
  const realtimeDataService = useMemo(
    () => new RealtimeDataService(supabase, queryClient),
    [supabase, queryClient]
  );

  // Generate unique subscriber ID for this hook instance
  const subscriberIdRef = useRef<string>(
    `subscriber-${Date.now()}-${Math.random()}`
  );
  if (!subscriberIdRef.current) {
    subscriberIdRef.current = `subscriber-${Date.now()}-${Math.random()}`;
  }

  useEffect(() => {
    if (!supabase || !tenantUuid || !user || isLoggingOut) return;

    // Add a small delay to prevent rapid mount/unmount cycles
    const timeoutId = setTimeout(() => {
      const manager = UnifiedSubscriptionManager.getInstance(
        tenantUuid,
        supabase,
        queryClient,
        realtimeDataService,
        userDatabaseId
      );

      manager.addSubscriber(subscriberIdRef.current!);
    }, 50); // 50ms delay to debounce rapid mounts

    return () => {
      clearTimeout(timeoutId);

      // Only remove subscriber if we actually added one
      const manager = UnifiedSubscriptionManager.getInstance(
        tenantUuid,
        supabase,
        queryClient,
        realtimeDataService,
        userDatabaseId
      );

      manager.removeSubscriber(subscriberIdRef.current!);

      // Cleanup singleton if no more subscribers with longer delay
      setTimeout(() => {
        UnifiedSubscriptionManager.cleanup(tenantUuid);
      }, 2000); // Increased delay to 2 seconds for better stability
    };
  }, [
    supabase,
    tenantUuid,
    user,
    userDatabaseId,
    queryClient,
    realtimeDataService,
    isLoggingOut,
  ]);
}
