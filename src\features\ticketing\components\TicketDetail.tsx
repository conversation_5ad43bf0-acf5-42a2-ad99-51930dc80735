'use client';

import {
  DynamicFileUpload,
  UploadedFile,
} from '@/features/shared/components/DynamicFileUpload';
import { DynamicRichTextEditor } from '@/features/shared/components/DynamicRichTextEditor';
import { ProfileAvatar } from '@/features/shared/components/ProfileAvatar';
import { toast } from '@/features/shared/components/toast';
import { Button } from '@/features/shared/components/ui/button';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useUserDatabaseId } from '@/features/shared/hooks/useUserDatabaseId';
import { FileUploadService } from '@/lib/services/file-upload.service';
import { cn, countWords } from '@/lib/utils';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/features/shared/components/ui/dialog';
import { Send, Trash2 } from 'lucide-react';
import { useCallback, useRef, useState, useEffect, useMemo } from 'react';
// Legacy cache warming removed - React Query handles prefetching automatically
import {
  useTicketMessages,
  useAddTicketMessage,
  useOpenTicket,
} from '@/hooks/useTickets';

import { useTenantUuid } from '@/hooks/useRealtimeQuery';
import {
  getUserCacheService,
  CachedUserProfile,
} from '@/lib/services/user-cache.service';
import { useQueryClient } from '@tanstack/react-query';
import { useSupabaseClient } from '@/lib/supabase-clerk';
// Real-time message updates now handled by React Query automatically
import { useReplyDraftPersistence } from '../hooks/useReplyDraftPersistence';
import { useCollapsedMessages } from '../hooks/useCollapsedMessages';
import { ReplyDraftConfirmDialog } from './ReplyDraftConfirmDialog';
import { CollapsedMessagesIndicator } from './CollapsedMessagesIndicator';
import { ResolveCheckbox } from './ResolveCheckbox';
import { ConfirmationDialog } from './ConfirmationDialog';
import {
  Attachment,
  Department,
  Ticket,
  TicketMessage,
  TicketPriority,
  TicketStatus,
  TicketWithDetails,
  AssignedUser,
} from '../models/ticket.schema';
import type { UserRole } from '../utils/status-transitions';

// Type for ticket metadata that may contain assignedUser
interface TicketMetadata extends Record<string, unknown> {
  assignedUser?: AssignedUser;
}

// Type for API message with nested author object
interface APITicketMessage
  extends Omit<
    TicketMessage,
    'authorId' | 'authorName' | 'authorEmail' | 'authorAvatar'
  > {
  author?: {
    id: string;
    first_name?: string;
    last_name?: string;
    email: string;
    avatar_url?: string;
    role?: string;
  };
  author_id?: string;
  authorId?: string;
  authorName?: string;
  authorEmail?: string;
  authorAvatar?: string;
  created_at?: string;
  is_internal?: boolean;
  isInternal?: boolean;
}

import { TicketDetailSkeleton } from './skeletons/TicketDetailSkeleton';

import { MessageItem } from './MessageItem';

interface TicketDetailProps {
  ticket: TicketWithDetails | null;
}

/**
 * TicketDetail Component - Enhanced with Safe HTML Rendering
 *
 * This component properly displays ticket content from both data sources
 * with safe HTML rendering to prevent XSS attacks while preserving formatting:
 * - Mock data: ticket.messages[0].content (legacy structure)
 * - Real API data: ticket.description (current structure)
 *
 * Key Features:
 * - Safe HTML rendering with DOMPurify sanitization
 * - Support for both data structures (mock and real API)
 * - XSS protection for user-generated content
 * - Proper HTML formatting display
 *
 * <AUTHOR> Augster
 * @version 2.2 - Safe HTML Rendering (January 2025)
 */
type DisplayMessage = {
  id: string;
  author: {
    name: string;
    email?: string;
    avatarUrl?: string;
  };
  content: string;
  createdAt: Date;
  attachments?: Attachment[];
  isInternal?: boolean;
  isOptimistic?: boolean;
};

// Helper function to determine reply recipient information
function getReplyRecipientInfo(ticket: Ticket, currentUserRole?: string) {
  const isAgent = currentUserRole === 'agent';
  const isAdmin =
    currentUserRole === 'admin' || currentUserRole === 'super_admin';

  const createdByUser = ticket.metadata?.createdByUser as {
    role: string;
    name: string;
    email: string;
    avatar?: string;
  };
  // Use the assignedUser from the ticket metadata (from API response)
  const assignedUser =
    (ticket as TicketWithDetails).assignedUser ||
    (ticket.metadata as TicketMetadata)?.assignedUser;

  const createdByAdmin =
    createdByUser?.role === 'admin' || createdByUser?.role === 'super_admin';

  if (isAgent) {
    if (createdByAdmin) {
      return {
        name: createdByUser.name,
        email: createdByUser.email,
        avatar: createdByUser.avatar,
      };
    } else {
      return {
        name: ticket.userName,
        email: ticket.userEmail,
        avatar: ticket.userAvatar,
      };
    }
  }

  if (isAdmin) {
    // For admins, always reply to the assigned agent if ticket is assigned
    if (ticket.assignedTo && assignedUser) {
      return {
        name: assignedUser.name,
        email: assignedUser.email,
        avatar: assignedUser.avatar,
      };
    } else {
      // If no assignment, reply to the ticket creator
      return {
        name: ticket.userName,
        email: ticket.userEmail,
        avatar: ticket.userAvatar,
      };
    }
  }

  // Default for user - show assignee if ticket is assigned, otherwise Support Team
  if (ticket.assignedTo && assignedUser) {
    return {
      name: assignedUser.name,
      email: assignedUser.email,
      avatar: assignedUser.avatar,
    };
  } else {
    return {
      name: 'Support Team',
      email: '<EMAIL>',
      avatar: undefined,
    };
  }
}

// Legacy cache service removed - React Query handles caching
import { TicketDetailHeader } from './TicketDetailHeader';

export function TicketDetail({ ticket }: TicketDetailProps) {
  // CRITICAL FIX: Move all hooks to top to fix Rules of Hooks violation
  const [replyContent, setReplyContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [showDiscardDialog, setShowDiscardDialog] = useState(false);
  const [isResolveChecked, setIsResolveChecked] = useState(false);
  const [showReopenConfirm, setShowReopenConfirm] = useState(false);
  const [showCloseConfirm, setShowCloseConfirm] = useState(false);
  const [optimisticStatus, setOptimisticStatus] = useState<string | null>(null);
  const [optimisticPriority, setOptimisticPriority] =
    useState<TicketPriority | null>(null);
  const [optimisticDepartment, setOptimisticDepartment] =
    useState<Department | null>(null);
  const isInitialLoadRef = useRef(true);

  // Enhanced caching for real-time ticket metadata updates

  const [showDraftDialog, setShowDraftDialog] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Draft persistence hook
  const { saveDraft, loadDraft, clearDraft, hasValidContent } =
    useReplyDraftPersistence();

  const { user, role, tenantId } = useAuth();
  const { userDatabaseId } = useUserDatabaseId();
  const queryClient = useQueryClient();
  const { supabase } = useSupabaseClient();

  // Reset checkbox state when ticket changes to prevent state persistence across tickets
  useEffect(() => {
    setIsResolveChecked(false);
  }, [ticket?.id]);

  // Clear optimistic values when real ticket data updates
  useEffect(() => {
    if (
      ticket?.status &&
      optimisticStatus &&
      ticket.status !== optimisticStatus
    ) {
      // Real data has arrived and matches our optimistic update, clear it
      setOptimisticStatus(null);
    }
    if (
      ticket?.priority &&
      optimisticPriority &&
      ticket.priority !== optimisticPriority
    ) {
      setOptimisticPriority(null);
    }
    if (
      ticket?.department &&
      optimisticDepartment &&
      ticket.department !== optimisticDepartment
    ) {
      setOptimisticDepartment(null);
    }
  }, [
    ticket?.status,
    optimisticStatus,
    ticket?.priority,
    optimisticPriority,
    ticket?.department,
    optimisticDepartment,
  ]);

  // Compute current values (optimistic or real)
  const currentStatus = (optimisticStatus || ticket?.status) as TicketStatus;
  const currentPriority = optimisticPriority || ticket?.priority;
  const currentDepartment = optimisticDepartment || ticket?.department;

  // DEBUG: Log ticket status changes
  if (process.env.NODE_ENV === 'development') {
    console.log('🎫 TicketDetail:', {
      ticketId: ticket?.id,
      ticketStatus: ticket?.status,
      optimisticStatus,
      currentStatus,
      isOptimisticUpdate:
        optimisticStatus !== null ||
        optimisticPriority !== null ||
        optimisticDepartment !== null,
    });
  }

  // Helper function to check if current user is assigned to the ticket
  const isAssignedToCurrentUser = useMemo(() => {
    if (!ticket) return false;
    // Check both database UUID and Clerk ID for assignment
    return (
      ticket.assignedTo === userDatabaseId ||
      ticket.assignedToClerkId === user?.id
    );
  }, [ticket, userDatabaseId, user?.id]);

  // CRITICAL FIX: Convert subdomain to UUID for API validation
  const tenantUuidQuery = useTenantUuid(tenantId || '');
  const currentTenantId = tenantUuidQuery.data || tenantId;

  // Modern React Query hooks - 2025 patterns
  const messagesQuery = useTicketMessages(
    currentTenantId || '',
    ticket?.id || ''
  );
  const addMessageMutation = useAddTicketMessage(
    currentTenantId || '',
    ticket?.id || ''
  );

  const messages = messagesQuery.data || [];
  const isLoadingMessages = messagesQuery.isLoading;

  // Real-time message updates handled automatically by React Query via useRealtimeQuery
  // Legacy cache warming removed - React Query handles prefetching automatically
  // Optimistic updates now handled by React Query mutations
  const openTicketMutation = useOpenTicket(tenantId || '');

  // Simplified reply recipient - let ProfileAvatar handle avatar loading
  const replyRecipient = useMemo(() => {
    if (!ticket) return null;
    return getReplyRecipientInfo(ticket, role);
  }, [ticket, role]);

  // Load draft content when ticket changes
  useEffect(() => {
    if (!ticket?.id) return;

    isInitialLoadRef.current = true;
    const draft = loadDraft(ticket.id);
    if (draft && draft.trim().length > 0) {
      setReplyContent(draft);
    } else {
      setReplyContent('');
    }
    // Set initial load to false after a brief delay to prevent auto-save during load
    const timeoutId = setTimeout(() => {
      isInitialLoadRef.current = false;
    }, 200); // Increased delay to ensure RichTextEditor has time to initialize

    return () => clearTimeout(timeoutId);
  }, [ticket?.id, loadDraft]);

  // Handle opening ticket in detail view with optimistic UI
  const handleOpenTicketInDetail = useCallback(async () => {
    if (openTicketMutation.isPending || !ticket?.id) return;

    try {
      // OPTIMISTIC UI: Immediately set status to 'open' for instant feedback
      setOptimisticStatus('open');

      // React Query mutations handle optimistic updates automatically
      await openTicketMutation.mutateAsync(ticket.id);
      toast.success('Ticket Opened', {
        description: 'You can now reply to this ticket.',
      });
    } catch (error) {
      // Revert optimistic update on error
      setOptimisticStatus(null);
      console.error('Error opening ticket:', error);
      toast.error('Failed to Open Ticket', {
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred. Please try again.',
      });
    }
  }, [ticket?.id, openTicketMutation]);

  const handlePriorityChange = async (newPriority: TicketPriority) => {
    if (!ticket?.id || isSubmitting) return;

    try {
      setIsSubmitting(true);

      // Optimistic UI update - immediately update priority
      setOptimisticPriority(newPriority);

      const response = await fetch(`/api/tickets/${ticket.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tenant_id: tenantId,
          priority: newPriority,
        }),
      });

      if (response.ok) {
        // Success - the real-time subscription will update the actual ticket data
        toast.success('Priority Updated', {
          description: `Ticket priority has been changed to ${newPriority}.`,
        });
      } else {
        // Revert optimistic update on error
        setOptimisticPriority(null);
        const errorData = await response.text();
        console.error('API Error:', response.status, errorData);
        toast.error('Failed to Update Priority', {
          description: `Server error: ${response.status}`,
        });
      }
    } catch (error) {
      // Revert optimistic update on error
      setOptimisticPriority(null);
      console.error('Failed to update priority:', error);
      toast.error('Failed to Update Priority', {
        description:
          'An error occurred while updating the priority. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDepartmentChange = async (newDepartment: Department) => {
    if (!ticket?.id || isSubmitting) return;

    try {
      setIsSubmitting(true);

      // Optimistic UI update - immediately update department
      setOptimisticDepartment(newDepartment);

      const response = await fetch(`/api/tickets/${ticket.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tenant_id: tenantId,
          department: newDepartment,
        }),
      });

      if (response.ok) {
        // Success - the real-time subscription will update the actual ticket data
        toast.success('Department Updated', {
          description: `Ticket department has been changed to ${newDepartment}.`,
        });
      } else {
        // Revert optimistic update on error
        setOptimisticDepartment(null);
        const errorData = await response.text();
        console.error('API Error:', response.status, errorData);
        toast.error('Failed to Update Department', {
          description: `Server error: ${response.status}`,
        });
      }
    } catch (error) {
      // Revert optimistic update on error
      setOptimisticDepartment(null);
      console.error('Failed to update department:', error);
      toast.error('Failed to Update Department', {
        description:
          'An error occurred while updating the department. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAttachClick = () => {
    fileInputRef.current?.click();
  };

  const handleClearReply = () => {
    const wordCount = countWords(replyContent);

    // Show confirmation dialog if user has typed more than 5 words
    if (wordCount >= 5) {
      setShowDiscardDialog(true);
    } else {
      // Clear immediately if less than 5 words
      setReplyContent('');
      setUploadedFiles([]);
    }
  };

  const handleReopenTicket = () => {
    setShowReopenConfirm(true);
  };

  const handleCloseTicket = () => {
    setShowCloseConfirm(true);
  };

  const handleConfirmReopen = async () => {
    if (!ticket?.id || isSubmitting) return;

    try {
      setIsSubmitting(true);
      setShowReopenConfirm(false);

      // Optimistic UI update - immediately update status to 'open'
      setOptimisticStatus('open');

      const response = await fetch(`/api/tickets/${ticket.id}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tenant_id: tenantId,
          new_status: 'open',
        }),
      });

      if (response.ok) {
        // Success - the real-time subscription will update the actual ticket data
        // Keep optimistic status until real data arrives
        toast.success('Ticket Reopened', {
          description: 'The ticket has been successfully reopened.',
        });
      } else {
        // Revert optimistic update on error
        setOptimisticStatus(null);
        const errorData = await response.text();
        console.error('API Error:', response.status, errorData);
        toast.error('Failed to Reopen Ticket', {
          description: `Server error: ${response.status}`,
        });
      }
    } catch (error) {
      // Revert optimistic update on error
      setOptimisticStatus(null);
      console.error('Failed to reopen ticket:', error);
      toast.error('Failed to Reopen Ticket', {
        description:
          'An error occurred while reopening the ticket. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleConfirmClose = async () => {
    if (!ticket?.id || isSubmitting) return;

    try {
      setIsSubmitting(true);
      setShowCloseConfirm(false);

      // Optimistic UI update - immediately update status to 'closed'
      setOptimisticStatus('closed');

      const response = await fetch(`/api/tickets/${ticket.id}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tenant_id: tenantId,
          new_status: 'closed',
        }),
      });

      if (response.ok) {
        // Success - the real-time subscription will update the actual ticket data
        // Keep optimistic status until real data arrives
        toast.success('Ticket Closed', {
          description: 'The ticket has been successfully closed.',
        });
      } else {
        // Revert optimistic update on error
        setOptimisticStatus(null);
        const errorData = await response.text();
        console.error('API Error:', response.status, errorData);
        toast.error('Failed to Close Ticket', {
          description: `Server error: ${response.status}`,
        });
      }
    } catch (error) {
      // Revert optimistic update on error
      setOptimisticStatus(null);
      console.error('Failed to close ticket:', error);
      toast.error('Failed to Close Ticket', {
        description:
          'An error occurred while closing the ticket. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleConfirmDiscard = () => {
    setReplyContent('');
    setUploadedFiles([]);
    setShowDiscardDialog(false);
    // Clear draft when discarding
    if (ticket?.id) clearDraft(ticket.id);
  };

  // Draft persistence handlers
  const handleDraftSave = () => {
    if (!ticket?.id) return;
    saveDraft(ticket.id, replyContent);
    setShowDraftDialog(false);
  };

  const handleDraftDiscard = () => {
    if (ticket?.id) clearDraft(ticket.id);
    setReplyContent('');
    setUploadedFiles([]);
    setShowDraftDialog(false);
  };

  const handleCancelDiscard = () => {
    setShowDiscardDialog(false);
  };

  const handleReplySubmit = async () => {
    const wordCount = countWords(replyContent);

    if (isSubmitting || wordCount < 5) return;

    if (!currentTenantId) {
      toast.error('Error', {
        description: 'Tenant information not available',
        duration: 4000,
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Upload files first if any
      let attachmentIds: string[] = [];
      let attachmentData: Array<{
        id: string;
        name: string;
        type: string;
        size: number;
      }> = [];

      if (uploadedFiles.length > 0) {
        try {
          const uploadedAttachments =
            await FileUploadService.uploadFilesForMessageWithData(
              uploadedFiles,
              currentTenantId,
              ticket?.id || ''
            );
          attachmentIds = uploadedAttachments.map((att) => att.id);
          attachmentData = uploadedAttachments.map((att) => ({
            id: att.id,
            name: att.name,
            type: att.type,
            size: att.size,
          }));
        } catch (uploadError) {
          console.error('Failed to upload files:', uploadError);
          toast.error('Upload Failed', {
            description: 'Failed to upload attachments. Please try again.',
            duration: 4000,
          });
          return;
        }
      }

      // Modern React Query mutation - 2025 pattern
      await addMessageMutation.mutateAsync({
        content: replyContent,
        attachment_ids: attachmentIds,
        attachments: attachmentData,
        is_resolve_action: isResolveChecked,
      });

      // Clear the reply content, files, and resolve checkbox
      setReplyContent('');
      setUploadedFiles([]);
      setIsResolveChecked(false);
    } catch (error) {
      console.error('Failed to send reply:', error);
      toast.error('Failed to Send Reply', {
        description:
          'Please try again or contact support if the problem persists',
        duration: 4000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Smart cache warming when ticket is viewed
  // Legacy cache warming removed - React Query handles prefetching automatically

  const mainMessageContent =
    ticket?.messages?.[0]?.content || ticket?.description;
  const mainMessageAttachments =
    ticket?.messages?.[0]?.attachments || ticket?.attachments;

  // OPTIMISTIC UI FIX: Never show skeleton when we have optimistic status changes
  // This prevents the intermediate blank screen when opening tickets
  const isOptimisticUpdate =
    optimisticStatus !== null ||
    optimisticPriority !== null ||
    optimisticDepartment !== null;

  const allMessages: DisplayMessage[] = [];
  // OPTIMISTIC UI FIX: Always show ticket content, prioritizing available data
  if (ticket) {
    // CRITICAL FIX: Prioritize ticket description and title from ticket object for optimistic updates
    const content =
      ticket.description ||
      mainMessageContent ||
      ticket.title ||
      'Loading ticket content...';

    // DEBUG: Log message content
    if (process.env.NODE_ENV === 'development') {
      console.log('📝 Messages:', {
        ticketId: ticket.id,
        ticketDescription: ticket.description,
        mainMessageContent,
        ticketTitle: ticket.title,
        finalContent: content,
        messagesCount: messages.length,
        isOptimisticUpdate: isOptimisticUpdate,
      });
    }

    allMessages.push({
      id: 'original-ticket',
      author: {
        name: ticket.userName || 'Unknown User',
        email: ticket.userEmail || '',
        ...(ticket.userAvatar && { avatarUrl: ticket.userAvatar }),
      },
      content: content,
      createdAt: ticket.createdAt ? new Date(ticket.createdAt) : new Date(),
      attachments: mainMessageAttachments || ticket.attachments || [],
      isInternal: false,
    });
  }

  // Populate user cache from API messages
  const userCache = getUserCacheService(queryClient, supabase);

  // Handle messages which may come from API with nested author or from real-time with flat fields
  messages.forEach((msg: APITicketMessage) => {
    // Debug log to see the message structure
    console.log('📩 Processing message:', {
      id: msg.id,
      hasAuthor: !!msg.author,
      authorFields: msg.author ? Object.keys(msg.author) : [],
      authorName: msg.authorName,
      author: msg.author,
    });

    // CRITICAL FIX: Cache user data from API messages
    if (msg.author && msg.author.id && tenantId) {
      const profile: CachedUserProfile = {
        id: msg.author.id,
        name:
          `${msg.author.first_name || ''} ${msg.author.last_name || ''}`.trim() ||
          msg.author.email?.split('@')[0] ||
          'Unknown User',
        email: msg.author.email || '',
        role: msg.author.role || 'user',
      };
      if (msg.author.avatar_url) {
        profile.avatarUrl = msg.author.avatar_url;
      }
      userCache.cacheUserProfile(profile, tenantId);
      console.log('💾 Cached user from API message:', profile.name);
    }

    // Handle both API response format and schema format
    // CRITICAL FIX: Ensure user names are properly resolved from both real-time and API fallback data
    let authorName = msg.authorName; // Real-time format

    if (!authorName && msg.author) {
      // API format - build name from first_name and last_name
      const fullName =
        `${msg.author.first_name || ''} ${msg.author.last_name || ''}`.trim();
      if (fullName) {
        authorName = fullName;
      } else if (msg.author.email) {
        // Fallback to email username if no name
        authorName = msg.author.email.split('@')[0];
      }
    }

    // Final fallback
    if (!authorName) {
      console.warn('⚠️ No author name found for message:', msg.id);
      authorName = 'Unknown User';
    }

    // Transform attachments to match the Attachment interface
    const messageAttachments: Attachment[] = (msg.attachments || []).map(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (att: any) => ({
        id: att.id,
        name: att.file_name || att.name,
        type: att.file_type || att.type,
        size: att.file_size || att.size,
        url: `/api/attachments/${att.id}`,
        uploadedAt: new Date(att.created_at || att.uploadedAt),
      })
    );

    const messageAuthor: DisplayMessage['author'] = {
      name: authorName,
    };
    const authorEmail = msg.authorEmail || msg.author?.email;
    if (authorEmail) {
      messageAuthor.email = authorEmail;
    }
    const authorAvatar = msg.authorAvatar || msg.author?.avatar_url;
    if (authorAvatar) {
      messageAuthor.avatarUrl = authorAvatar;
    }

    allMessages.push({
      id: msg.id,
      author: messageAuthor,
      content: msg.content,
      createdAt: new Date(msg.created_at || msg.createdAt),
      attachments: messageAttachments,
      isInternal: msg.is_internal || msg.isInternal || false,
    });
  });

  // Use collapsed messages hook for performance optimization
  const {
    displayMessages,
    collapsedCount,
    isLoadingCollapsed,
    shouldShowCollapsedIndicator,
    expandCollapsedMessages,
  } = useCollapsedMessages({
    ticketId: ticket?.id || '',
    allMessages,
    isLoadingMessages: isLoadingMessages && !isOptimisticUpdate, // Don't show loading during optimistic updates
  });

  // Determine if reply functionality should be disabled
  // For agents: disable when ticket status is 'new' (not yet opened)
  // For all users: disable when ticket status is 'closed'
  const isReplyDisabled =
    (role === 'agent' && currentStatus === 'new') || currentStatus === 'closed';

  // CRITICAL FIX: Enhanced loading logic to prevent unnecessary skeleton loading
  // Show skeleton only when actually fetching non-cached data, not during optimistic updates

  // Only show skeleton when no ticket is provided AND we don't have optimistic updates
  if (!ticket && !isOptimisticUpdate) {
    return <TicketDetailSkeleton />;
  }

  // If we have optimistic updates, never show skeleton - always render with available data
  if (isOptimisticUpdate && ticket) {
    // Render immediately with optimistic data - no skeleton needed
  } else if (!ticket) {
    // Fallback skeleton only when absolutely no data is available
    return <TicketDetailSkeleton />;
  }

  // Check if we're showing cached data while fetching fresh data
  const isShowingCachedMessages = messagesQuery.isPlaceholderData;

  // SKELETON FIX: More restrictive skeleton loading logic
  // Only show skeleton when truly necessary (no existing data AND first-time loading)
  const shouldShowMessagesSkeleton =
    !isOptimisticUpdate && // Never show skeleton during optimistic updates
    messagesQuery.isFetching &&
    !messages.length &&
    !isShowingCachedMessages &&
    messagesQuery.isLoading && // Only show skeleton during initial loading, not background refetches
    !messagesQuery.isRefetching; // Don't show skeleton during refetches

  // DEBUG: Log when skeleton would show to help diagnose issues
  if (process.env.NODE_ENV === 'development' && shouldShowMessagesSkeleton) {
    console.log('🦴 Showing skeleton for messages:', {
      isOptimisticUpdate,
      isFetching: messagesQuery.isFetching,
      messagesLength: messages.length,
      isShowingCachedMessages,
      isLoading: messagesQuery.isLoading,
      isRefetching: messagesQuery.isRefetching,
      ticketId: ticket?.id,
    });
  }

  if (shouldShowMessagesSkeleton) {
    return <TicketDetailSkeleton />;
  }

  return (
    <div className='flex-1 flex flex-col overflow-hidden'>
      <TicketDetailHeader
        ticket={ticket}
        currentStatus={currentStatus}
        currentPriority={currentPriority}
        currentDepartment={currentDepartment}
        isOpeningTicket={openTicketMutation.isPending}
        handleOpenTicketInDetail={handleOpenTicketInDetail}
        handleReopenTicket={handleReopenTicket}
        handleCloseTicket={handleCloseTicket}
        handlePriorityChange={handlePriorityChange}
        handleDepartmentChange={handleDepartmentChange}
      />
      {/* Scrollable Content Area */}
      <div className='flex-1 overflow-y-auto min-h-0'>
        <div className='space-y-0'>
          {displayMessages.map((message, index) => {
            const isLastMessage = index === displayMessages.length - 1;
            const shouldShowCollapsedAfterFirst =
              shouldShowCollapsedIndicator && index === 0;

            // Don't show top border on last message when collapsed indicator is visible
            const shouldShowTopBorder =
              index > 0 && !(isLastMessage && shouldShowCollapsedIndicator);

            return (
              <div key={`message-${message.id}-${index}`}>
                <div
                  className={cn(
                    shouldShowTopBorder &&
                      'border-t border-gray-200 dark:border-gray-700'
                  )}
                >
                  <MessageItem
                    message={message}
                    isInitiallyExpanded={isLastMessage}
                    isLastMessage={isLastMessage}
                    currentUserEmail={user?.emailAddresses[0]?.emailAddress}
                    ticket={ticket}
                    totalMessageCount={allMessages.length}
                  />
                </div>

                {/* Show collapsed messages indicator after first message */}
                {shouldShowCollapsedAfterFirst && (
                  <CollapsedMessagesIndicator
                    count={collapsedCount}
                    onExpand={expandCollapsedMessages}
                    isLoading={isLoadingCollapsed}
                  />
                )}
              </div>
            );
          })}
        </div>

        {/* Reply section - Hidden for agents when ticket status is 'new' */}
        {!isReplyDisabled && (
          <div className='border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'>
            <div className='p-6'>
              <div className='space-y-4'>
                {replyRecipient && (
                  <div className='flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400'>
                    <ProfileAvatar
                      avatarUrl={replyRecipient.avatar || null}
                      name={replyRecipient.name}
                      email={replyRecipient.email}
                      className='h-8 w-8'
                      fallbackClassName='bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs font-medium'
                    />
                    <div className='flex items-center gap-2'>
                      <span>Reply to:</span>
                      <span className='font-medium'>
                        {replyRecipient.name} ({replyRecipient.email})
                      </span>
                      <Button
                        variant='ghost'
                        size='sm'
                        className='h-6 w-6 p-0 ml-1 hover:bg-gray-100 dark:hover:bg-gray-700'
                      >
                        ×
                      </Button>
                    </div>
                  </div>
                )}
                <DynamicRichTextEditor
                  value={replyContent}
                  onChange={(content) => {
                    setReplyContent(content);
                    // CRITICAL FIX: Prevent auto-save during initial load to avoid clearing drafts
                    if (ticket?.id && !isInitialLoadRef.current) {
                      if (hasValidContent(content)) {
                        saveDraft(ticket.id, content);
                      } else {
                        clearDraft(ticket.id);
                      }
                    }
                  }}
                  placeholder='Type your reply...'
                  className='min-h-24'
                  disabled={isSubmitting}
                  onAttachClick={handleAttachClick}
                />
                <DynamicFileUpload
                  files={uploadedFiles}
                  onFilesChange={setUploadedFiles}
                  disabled={isSubmitting}
                  fileInputRef={fileInputRef}
                />

                {/* Resolve Checkbox and Buttons - All Left Aligned */}
                <div className='space-y-3'>
                  {/* Resolve Checkbox */}
                  <div className='flex items-center'>
                    <ResolveCheckbox
                      checked={isResolveChecked}
                      onChange={setIsResolveChecked}
                      disabled={isSubmitting}
                      ticketStatus={currentStatus || 'new'}
                      userRole={role as UserRole}
                      isAssignedAgent={isAssignedToCurrentUser}
                    />
                  </div>

                  {/* Buttons - Left Aligned */}
                  <div className='flex items-center gap-3'>
                    <Button
                      onClick={handleReplySubmit}
                      disabled={isSubmitting || countWords(replyContent) < 5}
                      className='rounded-lg px-4 py-2 font-medium text-sm transition-all duration-200'
                    >
                      <Send className='h-4 w-4 mr-2' />
                      {isSubmitting
                        ? 'Sending...'
                        : isResolveChecked
                          ? 'Send & Resolve'
                          : 'Send'}
                    </Button>
                    <Button
                      type='button'
                      variant='outline'
                      onClick={handleClearReply}
                      disabled={isSubmitting}
                      className='rounded-lg px-4 py-2 font-medium text-sm border-gray-300 text-gray-700 hover:bg-gray-50 transition-all duration-200'
                    >
                      <Trash2 className='h-4 w-4 mr-2' />
                      Clear
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Resolved ticket notification */}
        {!isReplyDisabled && currentStatus === 'resolved' && (
          <div className='bg-muted/50 rounded-lg p-4 space-y-2 mx-6'>
            <h4 className='text-sm font-medium text-gray-700 dark:text-gray-300 mb-1'>
              This ticket has been marked as resolved.
            </h4>
            <p className='text-xs text-muted-foreground'>
              {role === 'user'
                ? "If you're satisfied with the resolution, you're welcome to reply and let us know. If you still have any concerns, please reply with your feedback, and we will reopen the ticket to assist you further. If we don't hear from you within 7 days, the ticket will automatically close."
                : "You can now wait for the user's reply. If no reply is received within 7 days, this ticket will automatically close. You can also manually reopen or close the ticket using the buttons above."}
            </p>
          </div>
        )}

        {/* Message for agents when ticket is in 'new' status */}
        {isReplyDisabled && currentStatus === 'new' && (
          <div className='border-t border-gray-200 dark:border-gray-700 bg-amber-50 dark:bg-amber-900/20'>
            <div className='p-6 text-center'>
              <div className='text-amber-600 dark:text-amber-400 text-sm font-medium mb-2'>
                Ticket Not Yet Opened
              </div>
              <div className='text-amber-700 dark:text-amber-300 text-sm'>
                You must open this ticket before you can reply to it. Click
                &quot;Open This Ticket&quot; above to begin working on this
                ticket.
              </div>
            </div>
          </div>
        )}

        {/* Message when ticket is closed */}
        {isReplyDisabled && currentStatus === 'closed' && (
          <div className='border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/20'>
            <div className='p-6 text-center'>
              <div className='text-gray-600 dark:text-gray-400 text-sm font-medium mb-2'>
                Ticket Closed
              </div>
              <div className='text-gray-700 dark:text-gray-300 text-sm'>
                This ticket has been closed.{' '}
                {role !== 'user'
                  ? 'Use the "Reopen this ticket" button above to reopen it if needed.'
                  : 'Contact support if you need to reopen this ticket.'}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Discard Reply Confirmation Dialog */}
      <Dialog open={showDiscardDialog} onOpenChange={setShowDiscardDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Discard Reply?</DialogTitle>
            <DialogDescription>
              You have unsaved changes in your reply. Are you sure you want to
              discard them?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className='gap-2'>
            <Button variant='outline' onClick={handleCancelDiscard}>
              Cancel
            </Button>
            <Button onClick={handleConfirmDiscard}>Discard Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Draft Confirmation Dialog */}
      <ReplyDraftConfirmDialog
        open={showDraftDialog}
        onOpenChange={setShowDraftDialog}
        onDiscard={handleDraftDiscard}
        onSave={handleDraftSave}
      />

      {/* Reopen Ticket Confirmation Dialog */}
      <ConfirmationDialog
        open={showReopenConfirm}
        onOpenChange={setShowReopenConfirm}
        title='Reopen Ticket'
        description='Are you sure you want to reopen this ticket? This will change the status back to open and allow new replies.'
        onConfirm={handleConfirmReopen}
      />

      {/* Close Ticket Confirmation Dialog */}
      <ConfirmationDialog
        open={showCloseConfirm}
        onOpenChange={setShowCloseConfirm}
        title='Close Ticket'
        description='Are you sure you want to close this ticket? This action will permanently close the ticket and move it to closed tickets.'
        onConfirm={handleConfirmClose}
      />
    </div>
  );
}
