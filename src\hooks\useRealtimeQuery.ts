import { useEffect, useMemo } from 'react';
import {
  useQuery,
  useQueryClient,
  UseQueryOptions,
} from '@tanstack/react-query';
import { useSupabaseClient } from '@/lib/supabase-clerk';
import RealtimeDataService from '@/lib/services/realtime-data.service';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useAuth as useClerkAuth, useSession } from '@clerk/nextjs';

/**
 * ✅ Recommended Pattern: useRealtimeQuery() Custom Hook (Modern Supabase + React Query)
 *
 * Based on latest 2025 patterns from Docs/LatestPatterns.md
 *
 * This hook encapsulates:
 * - Fetching data with React Query
 * - Subscribing to Supabase real-time changes
 * - Automatically updating the cache when DB changes occur
 *
 * Benefits:
 * - Minimal, modern, robust
 * - Clean separation of concerns
 * - No manual subscription management
 * - Automatic cache updates on real-time events
 */
export function useRealtimeQuery<T extends { id: string }>(
  queryKey: string[],
  fetchFn: () => Promise<T[]>,
  _table: string,
  options?: {
    filter?: string;
    schema?: string;
    queryOptions?: Omit<UseQueryOptions<T[]>, 'queryKey' | 'queryFn'>;
  }
) {
  const { supabase } = useSupabaseClient();
  const queryClient = useQueryClient();

  // Initialize RealtimeDataService for proper user data transformation
  // CRITICAL FIX: Pass queryClient for cache-first user lookups
  const realtimeDataService = useMemo(
    () => new RealtimeDataService(supabase, queryClient),
    [supabase, queryClient]
  );

  // Memoize queryKey to prevent subscription churn
  const stableQueryKey = useMemo(() => queryKey, [queryKey]);

  // Memoize options to prevent subscription churn
  const stableOptions = useMemo(() => options, [options]);

  // React Query for data fetching
  const query = useQuery({
    queryKey: stableQueryKey,
    queryFn: fetchFn,
    ...stableOptions?.queryOptions,
  });

  // REMOVED: Direct real-time subscription to enforce single-connection architecture
  // All real-time updates now handled by useUnifiedRealtimeSubscription
  // This ensures only ONE connection per tenant in production

  // Keep realtimeDataService available for potential future use
  void realtimeDataService;

  return query;
}

// Types needed for tickets
interface RoleBasedFilterContext {
  tenantId: string;
  role: string;
  userId?: string;
  email?: string;
}

interface TicketFilterOptions {
  status?: string;
  priority?: string;
  assignedTo?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  roleFilter?: 'new' | 'assigned' | 'all';
}

/**
 * Hook to resolve tenant UUID from subdomain
 */
export function useTenantUuid(tenantId: string) {
  const { supabase } = useSupabaseClient();

  return useQuery({
    queryKey: ['tenant-uuid', tenantId],
    queryFn: async () => {
      // If already a UUID, return it
      if (
        tenantId.match(
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
        )
      ) {
        return tenantId;
      }

      // Convert subdomain to UUID
      const { data: tenantData, error: tenantError } = await supabase
        .from('tenants')
        .select('id')
        .eq('subdomain', tenantId)
        .single();

      if (tenantError || !tenantData) {
        throw new Error(`Tenant '${tenantId}' not found`);
      }

      return tenantData.id;
    },
    staleTime: 1000 * 60 * 60, // 1 hour - tenant UUIDs rarely change
    gcTime: 1000 * 60 * 60 * 24, // 24 hours
    enabled: !!tenantId,
  });
}

/**
 * Specialized hook for tickets with unified real-time updates
 * Compatible with useTickets signature for drop-in replacement
 * Uses centralized subscription manager for data consistency
 */
export function useRealtimeTickets(
  context: RoleBasedFilterContext,
  options?: TicketFilterOptions & { enabled?: boolean }
) {
  // Note: Using API route instead of direct Supabase client for proper authentication
  const clerkAuth = useClerkAuth();
  const { getToken, isLoaded, isSignedIn } = clerkAuth;
  const { session } = useSession();
  const { isLoggingOut } = useAuth();
  const { enabled = true, ...filterOptions } = options || {};

  // CRITICAL FIX: Add session status check to prevent authentication race condition
  // This ensures the session is truly active before attempting token generation
  const isSessionActive =
    isLoaded && isSignedIn && session?.status === 'active' && !isLoggingOut;

  // Resolve tenant UUID first
  const tenantUuidQuery = useTenantUuid(context.tenantId);
  const tenantUuid = tenantUuidQuery.data;

  // Memoize filterOptions to prevent subscription churn
  const stableFilterOptions = useMemo(() => filterOptions, [filterOptions]);

  // Extract complex expression for dependency array
  const stableFilterOptionsString = JSON.stringify(stableFilterOptions || {});

  // Memoize queryKey to prevent subscription churn
  const queryKey = useMemo(
    () => [
      'tickets',
      tenantUuid || 'loading',
      'list',
      stableFilterOptionsString,
    ],
    [tenantUuid, stableFilterOptionsString]
  );

  // Use standard React Query without real-time subscription (handled by unified manager)
  const query = useQuery({
    queryKey,
    queryFn: async () => {
      console.log('🔐 Query function called:', {
        isLoaded,
        isSignedIn,
        isSessionActive,
        tenantUuid,
        contextTenantId: context.tenantId,
      });

      if (!tenantUuid) {
        throw new Error('Tenant UUID not resolved');
      }

      // CRITICAL FIX: Use API route instead of direct Supabase client to ensure proper authentication
      const searchParams = new URLSearchParams({
        tenant_id: tenantUuid,
      });

      // Add role-based filtering parameters
      if (stableFilterOptions?.roleFilter) {
        searchParams.append('role_filter', stableFilterOptions.roleFilter);
      }

      // Add status filtering
      if (
        stableFilterOptions?.status &&
        Array.isArray(stableFilterOptions.status)
      ) {
        searchParams.append('status', stableFilterOptions.status.join(','));
      } else if (stableFilterOptions?.status) {
        searchParams.append('status', stableFilterOptions.status);
      }

      console.log('🔐 Attempting to get token...');

      // PROPER FIX: Generate token with proper error handling
      const token = await getToken({ template: 'supabase' });

      console.log('🔐 Token received:', !!token);

      if (!token) {
        throw new Error(
          'Unable to authenticate the request, you need to supply an active session'
        );
      }

      console.log(
        '🔐 Making API request to:',
        `/api/tickets?${searchParams.toString()}`
      );

      const response = await fetch(`/api/tickets?${searchParams.toString()}`, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      console.log('🔐 API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.log('🔐 API error:', errorData);
        throw new Error(
          errorData.error || `HTTP ${response.status}: Failed to fetch tickets`
        );
      }

      const data = await response.json();
      const tickets = data || []; // API returns tickets array directly, not wrapped in data property

      console.log(
        '🔐 Tickets fetched successfully:',
        tickets.length,
        'tickets'
      );

      // API route already returns properly formatted tickets, no transformation needed
      return tickets;
    },
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 1000 * 60 * 30, // 30 minutes
    retry: (failureCount, error) => {
      console.log('🔐 Retry check:', {
        failureCount,
        error: error?.message,
        isSessionActive,
        isLoaded,
        isSignedIn,
      });

      // AUTHENTICATION FIX: Retry failed authentication requests up to 3 times
      // This handles the case where the first request fails due to timing issues
      if (
        error instanceof Error &&
        (error.message.includes('Authentication required') ||
          error.message.includes('Unable to authenticate') ||
          error.message.includes('401') ||
          error.message.includes('403'))
      ) {
        return failureCount < 3;
      }

      // For other errors, use default retry logic
      return failureCount < 1;
    },
    retryDelay: (attemptIndex) => {
      // Progressive delay for auth retries: 500ms, 1s, 2s
      const delay = Math.min(500 * Math.pow(2, attemptIndex), 2000);
      console.log('🔐 Retrying in', delay, 'ms, attempt', attemptIndex + 1);
      return delay;
    },
    enabled:
      enabled &&
      !!tenantUuid &&
      !tenantUuidQuery.isLoading &&
      !!context.tenantId && // Ensure we have a valid tenant context
      isSessionActive, // PROPER FIX: Use official Clerk authentication check
  });

  // REMOVED: Real-time subscription handled by useTicketMessages hook to prevent duplicate subscriptions
  // useUnifiedRealtimeSubscription(context.tenantId);

  // PROPER FIX: Auto-refetch when authentication state changes
  // This handles the case where the query was disabled due to auth timing
  useEffect(() => {
    if (
      isSessionActive &&
      !!tenantUuid &&
      enabled &&
      (!query.data || query.data.length === 0) &&
      !query.isFetching &&
      !query.isError
    ) {
      console.log('🔐 Authentication ready, refetching tickets');
      query.refetch();
    }
  }, [isSessionActive, tenantUuid, enabled, query]);

  return query;
}

/**
 * Interface for test entries
 */
export interface TestEntry {
  id: string;
  title: string;
  description: string;
  created_at: string;
  tenant_id: string;
}

/**
 * Specialized hook for the test table
 */
export function useRealtimeTest(tenantId: string) {
  const { supabase } = useSupabaseClient();

  return useRealtimeQuery<TestEntry>(
    ['realtime-test', tenantId],
    async () => {
      // Use type assertion to bypass TypeScript schema inference issues
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data, error } = await (supabase as any)
        .from('realtime_test')
        .select('id, title, description, created_at, tenant_id')
        .eq('tenant_id', tenantId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Error fetching test entries:', error);
        throw error;
      }

      return (data || []) as TestEntry[];
    },
    'realtime_test',
    {
      filter: `tenant_id=eq.${tenantId}`,
      queryOptions: {
        staleTime: 0, // Always fresh for testing
        gcTime: 1000 * 60 * 5, // 5 minutes
      },
    }
  );
}
