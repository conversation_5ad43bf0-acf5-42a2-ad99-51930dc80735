{"mcpServers": {"playwright": {"command": "cmd", "args": ["/c", "npx", "C:/Users/<USER>/playwright-mcp/cli.js", "--config=playwright-config.json"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking@latest"]}, "taskmanager": {"command": "npx", "args": ["-y", "@paka/mcp-task-manager@latest"]}, "supabase": {"command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--read-only", "--project-ref=xprwqadnmauhpschgkwk"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}}}}