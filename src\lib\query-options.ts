/**
 * Query Options Factory - 2025 Optimized
 *
 * Centralized query options using TkDodo's best practices
 * All query functions use the existing API endpoints for seamless integration
 */

import { queryOptions } from '@tanstack/react-query';
import { QueryKeys, CACHE_CONFIG } from './query-keys';
import type { Ticket } from '@/features/ticketing/models/ticket.schema';

// API function types based on existing endpoints
interface TicketMessage {
  id: string;
  content: string;
  author_id: string;
  created_at: string;
}

interface ApiUserResponse {
  id: string;
  email: string;
  name: string;
  role: string;
  status: string;
  avatar_url?: string;
  clerk_id: string;
}

interface RoleBasedFilterContext {
  tenantId: string;
  role: string;
  userId?: string;
}

interface TicketFilterOptions {
  status?: string[];
  priority?: string[];
  roleFilter?: 'new' | 'assigned' | 'all';
}

// API functions that will call existing endpoints
const api = {
  tickets: {
    getTickets: async (
      context: RoleBasedFilterContext,
      options?: TicketFilterOptions
    ): Promise<Ticket[]> => {
      const params = new URLSearchParams({
        tenant_id: context.tenantId,
        user_role: context.role,
        role_filter: options?.roleFilter || 'assigned',
      });

      if (options?.status && options.status.length > 0) {
        params.set('status', options.status.join(','));
      }

      const response = await fetch(`/api/tickets?${params.toString()}`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch tickets: ${response.statusText}`);
      }

      return response.json();
    },

    getTicketById: async (
      tenantId: string,
      ticketId: string
    ): Promise<Ticket> => {
      const response = await fetch(
        `/api/tickets/${ticketId}?tenant_id=${tenantId}`,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch ticket: ${response.statusText}`);
      }

      const result = await response.json();
      // Extract the actual ticket data from the API response wrapper
      return result.data || result;
    },

    getTicketMessages: async (
      tenantId: string,
      ticketId: string
    ): Promise<TicketMessage[]> => {
      const response = await fetch(
        `/api/tickets/${ticketId}/messages?tenant_id=${tenantId}`,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch ticket messages: ${response.statusText}`
        );
      }

      const data = await response.json();
      return data.messages || [];
    },
  },

  users: {
    searchUsers: async (
      _tenantId: string,
      query: string,
      limit = 10
    ): Promise<ApiUserResponse[]> => {
      const params = new URLSearchParams({
        q: query,
        limit: limit.toString(),
      });

      const response = await fetch(`/api/users/search?${params.toString()}`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to search users: ${response.statusText}`);
      }

      const data = await response.json();
      return data.users || [];
    },

    getUserInfo: async (): Promise<{
      user: Record<string, unknown>;
      tenant: Record<string, unknown>;
    }> => {
      const response = await fetch('/api/user-info', {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch user info: ${response.statusText}`);
      }

      return response.json();
    },
  },

  tenants: {
    resolveTenant: async (
      subdomain: string
    ): Promise<{
      tenantId: string;
      tenant: Record<string, unknown>;
    }> => {
      const response = await fetch(
        `/api/tenants/resolve?subdomain=${subdomain}`,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to resolve tenant: ${response.statusText}`);
      }

      return response.json();
    },
  },

  settings: {
    getSettings: async (): Promise<{
      success: boolean;
      data: Record<string, unknown>;
    }> => {
      const response = await fetch('/api/settings', {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch settings: ${response.statusText}`);
      }

      return response.json();
    },

    updateUserSettings: async (
      updates: Record<string, unknown>
    ): Promise<{
      success: boolean;
      data: Record<string, unknown>;
    }> => {
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type: 'user', ...updates }),
      });

      if (!response.ok) {
        throw new Error(
          `Failed to update user settings: ${response.statusText}`
        );
      }

      return response.json();
    },

    updateAdminSettings: async (
      updates: Record<string, unknown>
    ): Promise<{
      success: boolean;
      data: Record<string, unknown>;
    }> => {
      const response = await fetch('/api/settings/admin', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error(
          `Failed to update admin settings: ${response.statusText}`
        );
      }

      return response.json();
    },
  },
};

// Query Options Factory
export const ticketQueryOptions = {
  list: (context: RoleBasedFilterContext, options?: TicketFilterOptions) =>
    queryOptions({
      queryKey: QueryKeys.TICKETS.list(context.tenantId, {
        ...(options?.roleFilter && { roleFilter: options.roleFilter }),
        ...(options?.status && { status: options.status }),
      }),
      queryFn: () => api.tickets.getTickets(context, options),
      staleTime: CACHE_CONFIG.tickets.staleTime,
      gcTime: CACHE_CONFIG.tickets.gcTime,
    }),

  detail: (tenantId: string, ticketId: string) =>
    queryOptions({
      queryKey: QueryKeys.TICKETS.detail(tenantId, ticketId),
      queryFn: () => api.tickets.getTicketById(tenantId, ticketId),
      // Apply enhanced 2025 cache configuration for optimal validation
      ...CACHE_CONFIG.tickets,
      // Serve cached data immediately while validating in background
      placeholderData: (previousData) => previousData,
    }),

  messages: (tenantId: string, ticketId: string) =>
    queryOptions({
      queryKey: QueryKeys.TICKETS.messages(tenantId, ticketId),
      queryFn: () => api.tickets.getTicketMessages(tenantId, ticketId),
      // Apply enhanced 2025 cache configuration for optimal validation
      ...CACHE_CONFIG.messages,
      // Serve cached data immediately while validating in background
      placeholderData: (previousData) => previousData,
    }),
};

export const userQueryOptions = {
  search: (tenantId: string, query: string, limit = 10) =>
    queryOptions({
      queryKey: QueryKeys.USERS.search(tenantId, query),
      queryFn: () => api.users.searchUsers(tenantId, query, limit),
      ...CACHE_CONFIG.users,
      enabled: query.length >= 3, // Only search when query is at least 3 characters
      // Serve cached data immediately while validating in background
      placeholderData: (previousData) => previousData,
    }),

  info: () =>
    queryOptions({
      queryKey: ['user', 'info'],
      queryFn: () => api.users.getUserInfo(),
      ...CACHE_CONFIG.users,
      // Serve cached data immediately while validating in background
      placeholderData: (previousData) => previousData,
    }),

  databaseId: (clerkId: string) =>
    queryOptions({
      queryKey: ['user-database-id', clerkId],
      queryFn: async () => {
        const response = await fetch('/api/user/database-id');
        if (!response.ok) {
          throw new Error(
            `Failed to fetch user database ID: ${response.status}`
          );
        }
        const { databaseId } = await response.json();
        return databaseId;
      },
      ...CACHE_CONFIG.users,
      // Override specific settings for database ID query
      staleTime: 5 * 60 * 1000, // 5 minutes - user database ID rarely changes
      gcTime: 30 * 60 * 1000, // 30 minutes
      enabled: false, // Disabled by default - should be enabled by sync-aware hooks only
    }),
};

export const tenantQueryOptions = {
  resolve: (subdomain: string) =>
    queryOptions({
      queryKey: ['tenant', 'resolve', subdomain],
      queryFn: () => api.tenants.resolveTenant(subdomain),
      staleTime: 60 * 60 * 1000, // 1 hour - tenant data rarely changes
      gcTime: 24 * 60 * 60 * 1000, // 24 hours
      enabled: !!subdomain,
    }),
};

export const settingsQueryOptions = {
  all: (tenantId: string, userId: string) =>
    queryOptions({
      queryKey: ['settings', tenantId, userId],
      queryFn: () => api.settings.getSettings(),
      ...CACHE_CONFIG.settings,
      enabled: false, // Disabled by default - should be enabled by sync-aware hooks only
      // Serve cached data immediately while validating in background
      placeholderData: (previousData) => previousData,
    }),
};

// Export the API functions for use in custom hooks
export { api };
